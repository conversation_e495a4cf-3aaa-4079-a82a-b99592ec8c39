<?php

namespace Src\Authentication\Application;

use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Mail\RegistrationMail;
class SocialAuthenticateUserService
{
    public function __construct()
    {
    }

    /**
     * @throws Exception
     */
    public function __invoke(string $email, string $name, string $token, string $provider = 'social_provider', string $image = null): string
    {
        $user = (new User)->where('email', $email)->first();
       
        if (!$user) {
            
            $user = new User();
            $user->name = $name;
            $user->profile_image = $image;
            $user->email = $email;
            $user->status = 'active';
            $user->email_verified_at = Carbon::now();
            if (str_contains($image, 'google')) {
                $user->google_id = $token;
            }
            $user->save();
            Mail::to($user->email)
                ->bcc(config('mail.from.address'))
                ->send(new RegistrationMail($user->name));
        }
        //if (!$user->profile_image) {
            $user->profile_image = $image;
            $user->save();
        //

        $this->ensureUserIsEnabled($user);
        return $user->createToken($provider)->plainTextToken;
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserIsEnabled(User $user): void
    {
        if ($user->status !== 'active') {
            throw new Exception('This user is not active');
        }
    }
}
