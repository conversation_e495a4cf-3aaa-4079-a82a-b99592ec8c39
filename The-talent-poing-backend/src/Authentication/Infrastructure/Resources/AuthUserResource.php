<?php

namespace Src\Authentication\Infrastructure\Resources;

use App\Models\Membership;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;
use Src\EmployerManagement\Infrastructure\Resources\CompanyResource;
use Src\FileManagement\Domain\File;
use Src\FileManagement\Infrastructure\Resources\FileResource;

/**
 * @mixin User
 */
class AuthUserResource extends JsonResource
{
    public function toArray($request): array
    {
        $signupCompleted = false;

        if ($this->role !== null && $this->password !== null) {
            if ($this->role === 'employer') {
                if ($this->company_id) {
                    $signupCompleted = true;
                }
            }
            if ($this->role === 'employee') {
                if ($this->where_currently_based !== null) {
                    $signupCompleted = true;
                }
            }
            if ($this->role === 'admin') {
                $signupCompleted = true;
            }
        }

        $membershipStatus = false;
        $plan = null;

        $membership = Membership::where('company_id', $this->company_id)
            ->orWhere('user_id', $this->id)
            ->first();

        if (!empty($membership)) {
            if ($membership->expire_at && now() < $membership->expire_at) {
                $membershipStatus = true;
                $plan = $membership->plan_id;
            }
        }

        return [
            'signup_completed' => $signupCompleted,
            'available_resume_count' => $this->available_resume_count,
            'company_id' => $this->company_id,
            'contact_no' => $this->contact_no,
            'currency' => $this->currency,
            'current_position' => $this->current_position,
            'current_salary' => $this->current_salary,
            'email' => $this->email,
            'id' => $this->id,
            'industry' => $this->industry,
            'is_approved' => $this->is_approved,
            'name' => $this->name,
            'nationality' => $this->nationality,
            'showcontact_no' => $this->showcontact_no,
            'isShowEmail' => $this->isShowEmail,
            'profile_complete_percentage' => $this->profile_complete_percentage,
            'profile_image' => new FileResource($this->whenLoaded('profile')),
            'company' => new CompanyResource($this->whenLoaded('company')),
            'role' => $this->role,
            'sector' => $this->sector,
            'slug' => $this->slug,
            'status' => $this->status,
            'website_url' => $this->website_url,
            'where_currently_based' => $this->where_currently_based,
            'where_job_search' => $this->where_job_search,
            'membership' => $membershipStatus,
            'plan' => $plan,
            'jobStatus' => $this->job_status,
            'is2FA' => $this->is2FA == 1 ? true : false,
            'first_login' => $this->first_login,
            'unlock_instant_apply' => $this->unlock_instant_apply,
            'date_of_birth' => $this->date_of_birth,
            'gender' => $this->gender
        ];
    }
}
