<?php

namespace Src\EmployerManagement\Application;

use App\Models\Company;
use App\Models\User;
use App\Models\Country;
use Exception;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UpdateEmployerService
{
    /**
     * @throws Exception
     */
    public function __invoke(int $employerId, array $data): Company
    {
        $user = (new User)->find($employerId);
//        $company = (new Company)->where('user_id', $user->id)->first();
        $this->ensureUserExist($user);

        $company = $user->company;

        if (!$company) {
            throw new Exception('The company not exist');
        }

        if (isset($data['company_name']))
        {
            if ($company->company_name != $data['company_name']) {
                $this->ensureCompanySlugNotExist($company, Str::slug($data['company_name'], '-'));
            }
        }

        if (isset($data['location_id']))
        {
            $location = Country::where('id', $data['location_id'])->first();
        }

        try {
            if (isset($data['user_email'])) {
                $this->ensureEmailIsValid($user, $data['user_email']);
                $user->email = $data['user_email'];
            }

            if (isset($data['user_name']))
            {
                $user->name = $data['user_name'];
            }

            if (isset($data['user_position']))
            {
                $user->current_position = $data['user_position'];
            }

            if (isset($data['password']) && $data['password'] !== '') {
                $user->password = Hash::make($data['password']);
            }
            $user->save();

            if (isset($data['company_name']))
            {
                $company->company_name = $data['company_name'];
            }

            if (isset($data['description'])) {
                $company->company_description = $data['description'];
            }

            if (isset($data['company_email']))
            {
                $company->company_email = $data['company_email'] ?? null;
            }

            if (isset($data['fk_logo_file_uuid']))
            {
                $company->fk_logo_file_uuid = $data['fk_logo_file_uuid'] ?? null;
            }

            if (isset($data['company_name']))
            {
                $company->company_slug = Str::slug($data['company_name'], '-') ?? null;
            }
            //$company->company_slug = $data['slug'] ?? null;

            if (isset($data['website']))
            {
                $company->company_website = $data['website'] ?? null;
            }

            if (isset($data['designation']))
            {
                $company->designation = $data['designation'] ?? null;
            }

            if (isset($data['location_id']))
            {
                $company->company_location = $data['location_id'] ?? 1;
            }

            if (isset($data['no_of_employees']))
            {
                $company->no_of_employees = $data['no_of_employees'] ?? null;
            }

            if (isset($data['linkedin_link']))
            {
                $company->linkedin_link = $data['linkedin_link'] ?? null;
            }

            if (isset($data['twitter_link']))
            {
                $company->twitter_link = $data['twitter_link'] ?? null;
            }

            if (isset($data['instagram_link']))
            {
                $company->instagram_link = $data['instagram_link'] ?? null;
            }

            if (isset($data['facebook_link']))
            {
                $company->facebook_link = $data['facebook_link'] ?? null;
            }

            if (isset($data['fk_logo_file_uuid']))
            {
                $company->fk_logo_file_uuid = $data['fk_logo_file_uuid'] ?? null;
            }

            if (isset($data['sector_id']))
            {
                $company->company_sector = $data['sector_id'] ?? null;
            }

            if (isset($data['company_name']) && isset($location))
            {
                $company->meta_tag = $data['company_name'] . ' Careers - The Talent Point';
                $company->meta_desc = 'Apply for jobs by '. $data['company_name'] . '. Register for Free & Search job openings at '. $data['company_name'] . '. Register for Free and Explore Careers in '. $location->country_name . '.';
            }

            $company->save();
            return $company;
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }

    }

    /**
     * @throws Exception
     */
    private function ensureUserExist(User $user = null): void
    {
        if (!$user) {
            throw new Exception('The user not exist');
        }
    }

    /**
     * @throws Exception
     */
    private function ensureEmailIsValid(User $user, string $email): void
    {
        if ($user->email !== $email) {
            $exist = (new User)->where('email', $email)->first();
            if ($exist) {
                throw new Exception('The email was already registered');
            }
        }
    }

    private function ensureCompanySlugNotExist(Company $company, string $slug): void
    {
        if ($company->company_slug !== $slug) {
            $exist = (new Company)->where('company_slug', $slug)->first();
            if ($exist) {
                throw new Exception('The company slug is already in use');
            }
        }
    }
}
