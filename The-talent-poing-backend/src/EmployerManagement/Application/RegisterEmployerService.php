<?php

namespace Src\EmployerManagement\Application;

use App\Models\Company;
use App\Models\Country;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class RegisterEmployerService
{
    /**
     * @throws Exception
     */
    public function __invoke(array $data): Company
    {
        $this->ensureUserNotExist($data['user_email']);
        $newSlug = $this->ensureCompanySlugNotExist(Str::slug($data['company_name'], '-'));
        $location = Country::where('id', $data['location_id'])->first();
        try {
            $user = new User();
            $user->role = User::$ROLE_EMPLOYER;
            $user->name = $data['user_name'];
            $user->email = $data['user_email'];
            $user->password = Hash::make($data['password']);
            $user->current_position = $data['user_position'];
            $user->save();
            $company = new Company();
            $company->user_id = $user->id;
            $company->company_name = $data['company_name'];
            $company->company_description = $data['description'] ?? null;
            $company->company_email = $data['company_email'] ?? null;
            $company->company_slug = $newSlug ?? Str::slug($data['company_name'], '-');
            //$company->company_slug = $data['slug'] ?? null;
            $company->company_website = $data['website'] ?? null;
            $company->designation = $data['designation'] ?? null;
            $company->company_location = $data['location_id'] ?? 1;
            $company->no_of_employees = $data['no_of_employees'] ?? null;
            $company->linkedin_link = $data['linkedin_link'] ?? null;
            $company->twitter_link = $data['twitter_link'] ?? null;
            $company->instagram_link = $data['instagram_link'] ?? null;
            $company->facebook_link = $data['facebook_link'] ?? null;
            $company->fk_logo_file_uuid = $data['fk_logo_file_uuid'] ?? null;
            $company->company_sector = $data['sector_id'] ?? null;
            $company->meta_tag = $data['company_name'] .' Careers - The Talent Point';
            $company->meta_desc = 'Apply for jobs by '. $data['company_name'] .'. Register for Free & Search job openings at '. $data['company_name'] .'. Register for Free and Explore Careers in '. $location->country_name .'.';
            $company->save();
            return $company;
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }

    }

    /**
     * @throws Exception
     */
    private function ensureUserNotExist(string $email)
    {
        $exist = User::whereEmail($email)->first();
        if ($exist) {
            throw new Exception('The email is already in use');
        }
    }
    private function ensureCompanySlugNotExist(string $slug)
    {
        $exist = Company::whereCompanySlug($slug)->first();
        if ($exist) {
            $newCompanySlug = $slug . '-' . rand(1000, 9999);
            return $newCompanySlug;
        }
    }
}
