<?php

namespace Src\EmployerManagement\Infrastructure\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Src\FileManagement\Infrastructure\Resources\FileResource;

class CandidateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'profile' => new FileResource($this->profile),
            'resume_pdf_path' => $this->resume_pdf_path,
            'sector_name' => $this->sector_name,
            'industries_name' => $this->industries_name,
            'country_name' => $this->country_name,
            'applicant_id' => $this->applicant_id,
            'viewed_user_name' => $this->viewed_user_name,
            'viewed_user_id' => $this->viewed_user_id,
            'job_status' => $this->job_status,
            'where_job_search' => $this->where_job_search,
            'skills' => $this->skills,
            'sector' => $this->sector,
            'industry' => $this->industry,
            'job_type' => $this->job_type,
            'years_of_experience' => $this->years_of_experience,
            'currency' => $this->currency,
            'desired_salary' => $this->desired_salary,
            'role' => $this->role,
            'status' => $this->status,
            'current_position' => $this->current_position,
            'slug' => $this->slug,
            'company_name' => $this->company_name,
            'company_slug' => $this->company_slug
        ];
    }
}
