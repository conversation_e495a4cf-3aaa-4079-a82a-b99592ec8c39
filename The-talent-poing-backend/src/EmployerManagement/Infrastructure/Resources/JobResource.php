<?php

namespace Src\EmployerManagement\Infrastructure\Resources;

use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Src\FileManagement\Infrastructure\Resources\FileResource;

/**
 * @mixin Job
 */
class JobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'company_id' => $this->company_id,
            'sector_id' => $this->sector_id,
            'job_title' => $this->job_title,
            'job_slug' => $this->job_slug,
            'job_description' => $this->job_description,
            'type_of_position' => $this->type_of_position,
            'job_country' => $this->job_country,
            'industry' => $this->industry,
            'experience' => $this->experience,
            'skills_required' => $this->skills_required,
            'monthly_fixed_salary_currency' => $this->monthly_fixed_salary_currency,
            'monthly_fixed_salary_min' => $this->monthly_fixed_salary_min,
            'monthly_fixed_salary_max' => $this->monthly_fixed_salary_max,
            'available_vacancies' => $this->available_vacancies,
            'deadline' => $this->deadline,
            'is_featured' => $this->is_featured,
            'hide_employer_details' => $this->hide_employer_details,
            'background_banner_image' => $this->background_banner_image,
            'meta_tag' => $this->meta_tag,
            'meta_desc' => $this->meta_desc,
            'job_type' => $this->job_type,
            'job_status' => $this->job_status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'job_city' => $this->job_city,
            'postal_code'=>$this->postal_code,
            'street_address'=>$this->street_address,
            'company' => new CompanyResource($this->whenLoaded('company')),
            'country' => $this->whenLoaded('country'),
            'banner' => new FileResource($this->whenLoaded('banner')),
            'skill' => $this->whenLoaded('skill'),
        ];
    }
}
