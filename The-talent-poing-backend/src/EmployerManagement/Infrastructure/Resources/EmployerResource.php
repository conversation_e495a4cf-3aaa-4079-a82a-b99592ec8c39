<?php

namespace Src\EmployerManagement\Infrastructure\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Src\Authentication\Domain\User;

/**
 * @mixin User
 */
class EmployerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $available_resume_count = null;

        if (isset($this->company))
        {
            $company = new CompanyResource($this->company);

            if (isset($company) && $company->available_resume_count)
            {
                $available_resume_count = $company->available_resume_count;

                if (((int) $available_resume_count) === 100000)
                {
                    $available_resume_count = 'Unlimited';
                }
            }
        }

        return [
            'available_resume_count' => $available_resume_count ?? $this->available_resume_count,
            'bio' => $this->bio,
            'card_cvv' => $this->card_cvv,
            'card_exp_month' => $this->card_exp_month,
            'card_exp_year' => $this->card_exp_year,
            'card_number' => $this->card_number,
            'card_type' => $this->card_type,
            'company' => $this->whenLoaded('company') ? new CompanyResource($this->company) : null,
            'created_by_id' => $this->created_by_id,
            'currency' => $this->currency,
            'designation' => $this->current_position,
            'current_salary' => $this->current_salary,
            'date_of_birth' => $this->date_of_birth,
            'desired_salary' => $this->desired_salary,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'facebook_link' => $this->facebook_link,
            'first_login' => $this->first_login,
            'gender' => $this->gender,
            'google_id' => $this->google_id,
            'id' => $this->id,
            'industry' => $this->industry,
            'instagram_link' => $this->instagram_link,
            'is2FA' => $this->is2FA,
            'isShowEmail' => $this->isShowEmail,
            'is_approved' => $this->is_approved,
            'job_status' => $this->job_status,
            'job_type' => $this->job_type,
            'linked_id' => $this->linked_id,
            'linkedin_link' => $this->linkedin_link,
            'login_count' => $this->login_count,
            'name' => $this->name,
            'nationality' => $this->nationality,
            'otp' => $this->otp,
            'profile_complete_percentage' => $this->profile_complete_percentage,
            'profile_image' => $this->profile_image,
            'role' => $this->role,
            'sector' => $this->sector,
            'showcontact_no' => $this->showcontact_no,
            'skills' => $this->skills,
            'slug' => $this->slug,
            'status' => $this->status,
            'twitter_link' => $this->twitter_link,
            'unlock_instant_apply' => $this->unlock_instant_apply,
            'updated_at' => $this->updated_at,
            'website_url' => $this->website_url,
            'where_currently_based' => $this->where_currently_based,
            'where_job_search' => $this->where_job_search,
            'years_of_experience' => $this->years_of_experience,
            'average_rating' => $this->whenLoaded('company', function () {
                return round(optional($this->company->reviews->first())->average_rating, 1);
            }),
            'total_reviews' => $this->whenLoaded('company', function () {
                return optional($this->company->reviews->first())->total_reviews;
            }),
            'team_members' => $this->whenLoaded('teamMembers', function () {
                return $this->teamMembers->map(function ($teamMember) {
                    return [
                        'id' => $teamMember->id,
                        'name' => $teamMember->name,
                        'email' => $teamMember->email,
                        'role' => $teamMember->role,
                        'designation' => $teamMember->current_position,
                        'available_resume_count' => $teamMember->available_resume_count,
                        'status' => $teamMember->status,
                    ];
                });
            }),
            'membership' => $this->whenLoaded('company', function () {
                return $this->company->membership ? [
                    'id' => $this->company->membership->id,
                    'plan_id' => $this->company->membership->plan_id,
                    'expire_at' => $this->company->membership->expire_at,
                    'purchase_at' => $this->company->membership->purchase_at,
                    'status' => $this->company->membership->status,

                    // add static field
                    'invoice_number' => null,
                    'information' => null,

                    'plan' => $this->company->membership->plan ? [
                        'id' => $this->company->membership->plan->id,
                        'title' => $this->company->membership->plan->plan_title,
                        'sub_desc' => $this->company->membership->plan->plan_sub_desc,
                        'currency' => $this->company->membership->plan->plan_currency,
                        'amount' => $this->company->membership->plan->plan_amount,
                        'type' => $this->company->membership->plan->plan_type,
                        'points' => $this->company->membership->plan->plan_points,
                        'status' => $this->company->membership->plan->status,
                    ] : null,
                ] : null;
            }),
            'active_job_count' => $this->whenLoaded('company', function () {
                return $this->company->jobs->where('job_status', 'active')->count();
            }), // Count of active jobs
            'claim_status' => $this->whenLoaded('company', function () {
                return $this->company->claim ? [
                    'id' => $this->company->claim->id,
                    'status' => $this->company->claim->status,
                    'message' => $this->company->claim->message,
                    'claim_date' => $this->company->claim->created_at,
                    'user' => $this->company->claim->user ? [
                        'id' => $this->company->claim->user->id,
                        'name' => $this->company->claim->user->name,
                        'email' => $this->company->claim->user->email,
                        'phone' => $this->company->claim->user->contact_no,
                        'role' => $this->company->claim->user->role,
                        'designation' => $this->company->claim->user->current_position,
                    ] : null,
                ] : null;
            }),
        ];
    }
}
