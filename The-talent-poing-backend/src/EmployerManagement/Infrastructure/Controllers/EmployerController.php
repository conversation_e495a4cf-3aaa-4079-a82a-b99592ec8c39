<?php

namespace Src\EmployerManagement\Infrastructure\Controllers;

use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use Src\AppFramework\ApiController;
use Src\EmployerManagement\Application\RegisterEmployerService;
use Src\EmployerManagement\Application\UpdateEmployerService;
use Src\EmployerManagement\Infrastructure\Resources\EmployerResource;

class EmployerController extends ApiController
{
    public function showEmployer($id): AnonymousResourceCollection
    {
        $employer = [];
        $user = (new User)
            ->with('ownCompany.logo', 'company', 'company.sector', 'company.location') // newly add this 'company' relation here
            ->where('id', $id)
            ->first();
        array_push($employer, $user);
        return EmployerResource::collection($employer);
    }

    public function updateEmployer(Request $request, $id): JsonResponse
    {
        // Define validation rules
        $validatedData = $request->validate([
            'user_name' => 'nullable|string|max:255',
            'user_email' => 'nullable|email|max:255',
            'user_position' => 'nullable|string|max:255',
            // 'password' => 'nullable|string|min:6',
            'company_name' => 'nullable|string|max:255',
            // 'description' => 'nullable|string',
            'company_email' => 'nullable|email|max:255',
            // 'fk_logo_file_uuid' => 'nullable|string',
            // 'website' => 'nullable|url',
            // 'designation' => 'nullable|string|max:255',
            'location_id' => 'nullable|integer',
            // 'no_of_employees' => 'nullable|integer',
            // 'linkedin_link' => 'nullable|url',
            // 'twitter_link' => 'nullable|url',
            // 'instagram_link' => 'nullable|url',
            // 'facebook_link' => 'nullable|url',
            'sector_id' => 'nullable|integer',
        ]);
        try {
            (new UpdateEmployerService)($id, $request->all());
            return $this->respondWithSuccess($request->all());
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return $this->respondError($e->getMessage());
        }
    }

    public function createEmployer(Request $request): JsonResponse
    {
        try {
            (new RegisterEmployerService)($request->all());
            return $this->respondWithSuccess($request->all());
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }
}
