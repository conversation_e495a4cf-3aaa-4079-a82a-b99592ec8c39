<?php

namespace Src\FileManagement\Application;

use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Src\FileManagement\Domain\FileContainer;

class GetParentContainersUseCase
{
    private array $permissions = [];
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @return Collection<FileContainer>|array<FileContainer>
     * @throws Exception
     */
    public function __invoke(): Collection|array
    {
        $this->ensureUserHasPermissions();
        return (new FileContainer)
            ->whereNull('fk_container_uuid')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserHasPermissions(): void
    {
        // if ([Verification]) {
        //     throw new Exception('This user has no permission to perform this action');
        // }
    }

}
