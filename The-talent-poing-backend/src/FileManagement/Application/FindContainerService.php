<?php

namespace Src\FileManagement\Application;

use DomainException;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Src\FileManagement\Domain\FileContainer;

class FindContainerService
{
    /**
     * @throws Exception
     */
    public function __invoke(string $containerUuid): FileContainer|null
    {
        $container = (new FileContainer)
            ->withTrashed()
            ->with('parentContainer')
            ->find($containerUuid);

        if (!$container) {
            throw new DomainException('Can\'t find this container');
        }

        return $container;
    }
}
