<?php

namespace Src\FileManagement\Application;

use App\Models\User;
use Carbon\Carbon;
use Exception;
use Src\FileManagement\Domain\File;
use Src\FileManagement\Domain\FileActivity;

class RegisterFileActivityUseCase
{
    private array $permissions = [];
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @param File $file
     * @param string|null $action
     * @param string|null $comment
     * @param User|null $user
     * @return FileActivity
     * @throws Exception
     */
    public function __invoke(File $file, string $action = null, string $comment = null, User $user = null): FileActivity
    {
        $this->ensureUserHasPermissions();

        $activity = new FileActivity;
        $activity->action = $action ?? 'comment';
        $activity->comment = $comment;
        $activity->fk_file_uuid = $file->uuid;
        if ($user) {
            $activity->fk_user_id = $user->id;
            //$activity->fk_requested_by_uuid = $this->user->id;
        } else {
            $activity->verified_at = Carbon::now();
            $activity->fk_user_id = $this->user->id;
        }
        $activity->save();

        return $activity;
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserHasPermissions(): void
    {
        // if ([Verification]) {
        //     throw new Exception('This user has no permission to perform this action');
        // }
    }

}
