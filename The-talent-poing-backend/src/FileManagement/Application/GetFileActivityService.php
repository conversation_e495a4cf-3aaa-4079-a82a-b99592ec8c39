<?php

namespace Src\FileManagement\Application;

use App\Models\User;
use DomainException;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Src\FileManagement\Domain\File;
use Src\FileManagement\Domain\FileActivity;
use Src\FileManagement\Domain\FileContainer;

class GetFileActivityService
{
    private array $permissions = [];
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }


    /**
     * @param string $fileUuid
     * @return Collection<FileActivity>
     * @throws Exception
     */
    public function __invoke(string $fileUuid): Collection
    {
        $this->ensureUserHasPermissions();

        return (new FileActivity)
            ->with('user')
            ->where('fk_file_uuid', $fileUuid)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserHasPermissions(): void
    {
        // if ([Verification]) {
        //     throw new Exception('This user has no permission to perform this action');
        // }
    }

}
