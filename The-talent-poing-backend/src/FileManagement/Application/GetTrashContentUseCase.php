<?php

namespace Src\FileManagement\Application;

use App\Models\User;
use Exception;
use JetBrains\PhpStorm\ArrayShape;
use Src\FileManagement\Domain\File;
use Src\FileManagement\Domain\FileContainer;

class GetTrashContentUseCase
{
    private array $permissions = [];
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @return array
     * @throws Exception
     */
    #[ArrayShape([
        'container' => "\Src\FileManagement\Domain\FileContainer",
        'containers' => "\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Src\FileManagement\Domain\FileContainer[]",
        'files' => "\Illuminate\Database\Eloquent\Collection|\Src\FileManagement\Domain\File[]"
    ])]
    public function __invoke(): array
    {
        $this->ensureUserHasPermissions();

        $containers = FileContainer::onlyTrashed()
            ->orderBy('created_at')
            ->get();
        $files = File::onlyTrashed()
            ->orderBy('created_at')
            ->get();

        return [
            'container' => ['uuid' => 'trash', 'name' => 'Elementos borrados'],
            'containers' => $containers,
            'files' => $files
        ];
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserHasPermissions(): void
    {
        // if ([Verification]) {
        //     throw new Exception('This user has no permission to perform this action');
        // }
    }
}
