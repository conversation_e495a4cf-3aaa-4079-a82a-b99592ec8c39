<?php

namespace Src\FileManagement\Application;

use App\Models\User;
use Exception;
use JetBrains\PhpStorm\ArrayShape;
use Src\FileManagement\Domain\File;

class GetStorageServiceStatusUseCase
{
    private array $permissions = [];
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @return array
     * @throws Exception
     */
    #[ArrayShape(['usage' => "int|mixed", 'total' => "int", 'payment' => "float"])]
    public function __invoke(): array
    {
        $this->ensureUserHasPermissions();
        $usage = (new File)
            ->withTrashed()
            ->sum('size');
        $total = 500000000000;
        return [
            'usage' => $usage,
            'total' => $total,
            'payment' => ($usage / 1000000000) * 0.07
        ];
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserHasPermissions(): void
    {
        // if ([Verification]) {
        //     throw new Exception('This user has no permission to perform this action');
        // }
    }
}
