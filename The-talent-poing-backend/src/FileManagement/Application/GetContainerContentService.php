<?php

namespace Src\FileManagement\Application;

use App\Models\User;
use Exception;
use JetBrains\PhpStorm\ArrayShape;
use Src\FileManagement\Domain\FileContainer;

class GetContainerContentService
{
    private array $permissions = [];
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @param string $containerUuid
     * @return array
     * @throws Exception
     */
    #[ArrayShape([
        'container' => "\Src\FileManagement\Domain\FileContainer",
        'containers' => "\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Src\FileManagement\Domain\FileContainer[]",
        'files' => "\Illuminate\Database\Eloquent\Collection|\Src\FileManagement\Domain\File[]"
    ])]
    public function __invoke(string $containerUuid): array
    {
        $this->ensureUserHasPermissions();

        $container = (new FindContainerService($this->user))($containerUuid);

        $containers = FileContainer::whereFkContainerUuid($container->uuid)
            ->orderBy('created_at')
            ->get();
        $files = $container
            ->files()
            ->orderBy('created_at')
            ->get();

        return ['container' => $container, 'containers' => $containers, 'files' => $files];
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserHasPermissions(): void
    {
        // if ([Verification]) {
        //     throw new Exception('This user has no permission to perform this action');
        // }
    }
}
