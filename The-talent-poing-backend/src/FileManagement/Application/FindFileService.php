<?php

namespace Src\FileManagement\Application;

use App\Models\User;
use DomainException;
use Exception;
use Src\FileManagement\Domain\File;

class FindFileService
{
    private array $permissions = [];

    /**
     * @throws Exception
     */
    public function __invoke(string $fileUuid, bool $includeTrash = false): File
    {
        $file = (new File);
        if($includeTrash){
            $file->withTrashed();
        }
        $entry = $file->find($fileUuid);
        if (!$entry) {
            throw new DomainException('Can\'t find this file');
        }

        return $entry;
    }
}
