<?php

namespace Src\FileManagement\Application;

use DomainException;
use Src\FileManagement\Domain\File;

class DestroyFileService
{
    /**
     * @param string $fileUuid
     * @param bool $force
     * @return File
     */
    public function __invoke(string $fileUuid, bool $force = false): File
    {
        $file = (new File)
            ->withTrashed()
            ->find($fileUuid);

        if (!$file) {
            throw new DomainException('Can\'t find this file');
        }
        if ($file->deleted_at || $force) {
            $file->forceDelete();
        } else {
            $file->delete();
        }
        return $file;
    }
}
