<?php

use App\Http\Controllers\Api\AdminSettingsController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AuthorController;
use App\Http\Controllers\Api\AutomationController;
use App\Http\Controllers\Api\ClaimCompanyController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\CountriesController;
use App\Http\Controllers\Api\JobsController;
use App\Http\Controllers\Api\TestimonialController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\SectorController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\MigrationController;
use App\Http\Controllers\Api\CollectionController;
use App\Http\Controllers\Api\CompanySearchController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Src\EmployerManagement\Infrastructure\Controllers\EmployerController;
use Src\FileManagement\Infrastructure\Controllers\FilesController;
use Illuminate\Support\Str;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum'])->group(function () {

    Route::prefix('authentication')->group(function () {
        Route::get('session', [AuthController::class, 'getSession']);
        Route::get('logout', [AuthController::class, 'logout']);
    });

    Route::prefix('jobs')->group(function () {
        Route::post('{id}/toggle-favorite', [JobsController::class, 'toggleFavorite']);
    });

    Route::prefix('file-management')->group(function () {
        Route::apiResource('files', FilesController::class);
    });

    Route::group(['prefix' => 'notifications'], function ($router) {
        Route::get('/', [NotificationController::class, 'getNotifications']);
        Route::post('/save/notifications/{userId}', 'App\Http\Controllers\Api\NotificationController@saveNotification');
        Route::put('/updatereadunreadnotificationstatus', 'App\Http\Controllers\Api\NotificationController@updateReadUnreadNotificationStatus');
        Route::get('/getreadnotifications', 'App\Http\Controllers\Api\NotificationController@getReadNotifications');
        Route::put('/updatenotificationstatus/{id}', 'App\Http\Controllers\Api\NotificationController@updatenotificationstatus');
    });

    Route::group(['prefix' => 'users'], function () {
        Route::post('signup/update-role', [UserController::class, 'updateUserRole']);
        Route::post('signup/update-info', [UserController::class, 'updateEmployeeTellUsInfo']);
        Route::post('signup/complete', [UserController::class, 'updateUserAccountInfo']);
    });

    Route::group(['prefix' => 'jobs'], function () {
        Route::get('/', [JobsController::class, 'index']);
        Route::post('/', [JobsController::class, 'createJob']);

        Route::get('/getalljobs', 'App\Http\Controllers\Api\JobsController@getAllJobs');
        Route::get('/getcurrentuseralljobs', 'App\Http\Controllers\Api\JobsController@getCurrentUserAllJobs');
        Route::get('/getsinglejobs/{id}', 'App\Http\Controllers\Api\JobsController@getSingleJob');
        Route::get('/getsortbyjobs/{option}', 'App\Http\Controllers\Api\JobsController@getSortByJobs');
        Route::put('/updatejobs/{id}', 'App\Http\Controllers\Api\JobsController@updateJob');
        Route::delete('/jobs/{id}', 'App\Http\Controllers\Api\JobsController@deleteJob');
        Route::get('/getusersavedjobs', 'App\Http\Controllers\Api\JobsController@getUserSavedJobs');
        Route::get('/getActiveJobs', 'App\Http\Controllers\Api\JobsController@getActiveJobs');
        Route::put('/updatesinglejobbackgroundbannerimage', 'App\Http\Controllers\Api\JobsController@updateSingleJobBackgroundBannerImage');
        Route::get('/getalljobfilter/{user_id}', 'App\Http\Controllers\Api\JobsController@getAlljobfilter');
        Route::put('/updatesavejobfilter', 'App\Http\Controllers\Api\JobsController@UpdateSaveJobFilter');
        Route::post('/jobfilter', 'App\Http\Controllers\Api\JobsController@DeleteJobFilter');

        Route::get('/getuseralljobs', 'App\Http\Controllers\Api\JobsController@getUserAllJobs');

        /**
         * @deprecated
         */
        //Route::post('addsavedjob', 'App\Http\Controllers\Api\JobsController@addFavorites');
        Route::delete('unsavedjob/{id}', 'App\Http\Controllers\Api\JobsController@deleteUnSavedJob');
        Route::get('/getemployeractivejobs', 'App\Http\Controllers\Api\JobsController@getEmployerActiveJobs');
        Route::get('/getusersinglejobs', [JobsController::class, 'getUserSingleJob']);
    });

    Route::group(['prefix' => 'companies'], function () {
        Route::post('/', [CompanyController::class, 'createCompany']);
        Route::get('getcompany', [CompanyController::class, 'getAllCompany']);
        Route::get('/{id}', 'App\Http\Controllers\Api\CompanyController@getSingleEmployerCompanyDetails');
        Route::put('/{id}', 'App\Http\Controllers\Api\CompanyController@updateSetCompanyInfo');
        Route::post('/{id}/background-banner-image', 'App\Http\Controllers\Api\CompanyController@updateSingleCompanyBackgroundBannerImage');

        Route::put('updatecompany/{id}', 'App\Http\Controllers\Api\CompanyController@updateCompany');
        Route::delete('company/{id}', 'App\Http\Controllers\Api\CompanyController@deleteCompany');
        // Route for updating company resume count
        Route::get('getsinglecompanydetails/{user_id}', 'App\Http\Controllers\Api\CompanyController@getSingleCompanyDetails');
        Route::put('updatecompanysociallinks/{id}', 'App\Http\Controllers\Api\CompanyController@updateCompanySocialLinks');
        Route::post('updatecompanylogo', 'App\Http\Controllers\Api\CompanyController@updateCompanyLogo');
        Route::put('updatecompanyinfo/{id}', 'App\Http\Controllers\Api\CompanyController@updateSetCompanyInfo');
        Route::put('/updatesinglecompanybackgroundbannerimage', 'App\Http\Controllers\Api\CompanyController@updateSingleCompanyBackgroundBannerImage');
    });
});

Route::group(['prefix' => 'companies'], function () {
    Route::post('/', [CompanyController::class, 'createCompany']);
    Route::get('getcompany', [CompanyController::class, 'getAllCompany']);
    Route::get('/{id}', 'App\Http\Controllers\Api\CompanyController@getSingleEmployerCompanyDetails');
    Route::put('/{id}', 'App\Http\Controllers\Api\CompanyController@updateSetCompanyInfo');
    Route::post('/{id}/background-banner-image', 'App\Http\Controllers\Api\CompanyController@updateSingleCompanyBackgroundBannerImage');

    Route::put('updatecompany/{id}', 'App\Http\Controllers\Api\CompanyController@updateCompany');
    Route::delete('company/{id}', 'App\Http\Controllers\Api\CompanyController@deleteCompany');
    // Route for updating company resume count
    Route::get('getsinglecompanydetails/{user_id}', 'App\Http\Controllers\Api\CompanyController@getSingleCompanyDetails');
    Route::put('updatecompanysociallinks/{id}', 'App\Http\Controllers\Api\CompanyController@updateCompanySocialLinks');
    Route::post('updatecompanylogo', 'App\Http\Controllers\Api\CompanyController@updateCompanyLogo');
    Route::put('updatecompanyinfo/{id}', 'App\Http\Controllers\Api\CompanyController@updateSetCompanyInfo');
    Route::put('/updatesinglecompanybackgroundbannerimage', 'App\Http\Controllers\Api\CompanyController@updateSingleCompanyBackgroundBannerImage');
});

Route::group(['prefix' => 'plan'], function () {
    Route::post('purchase', 'App\Http\Controllers\Api\AuthController@purchasePlan');
});

Route::get('/update-job-user-id', [JobsController::class, 'updateUserIdInJobs']);

Route::post('/update-number', [AuthController::class, 'updateNumber']);

Route::post('/update-role', [AuthController::class, 'updateRole']);


Route::group(['middleware' => ['api']], function () {
    Route::post('authenticate', [AuthController::class, 'authenticate']);
    Route::post('social-authenticate', [AuthController::class, 'socialAuthenticate']);

    Route::post('login', [AuthController::class, 'login']);
    Route::post('match-otp', [AuthController::class, 'matchOTP']);
    Route::post('signup/register', [AuthController::class, 'register']);
    Route::post('/logout', 'App\Http\Controllers\Api\AuthController@logout');
    Route::post('/refresh', 'App\Http\Controllers\Api\AuthController@refresh');
    Route::group(['prefix' => 'author'], function () {
        Route::get('/', [AuthorController::class, 'authors']);
        Route::get('/{slugOrId}', [AuthorController::class, 'singleAuthor']);
        Route::post('/create', [AuthorController::class, 'createOrUpdate']);
        Route::get('/delete/{id}', [AuthorController::class, 'delete']);
        Route::get('/search', [AuthorController::class, 'searchByName']);
    });
    Route::get('/markasdeleted', 'App\Http\Controllers\Api\JobsController@markAsDeleted');
    Route::group(['prefix' => 'jobs'], function () {
        Route::get('/{id}', [JobsController::class, 'getSingleJob']);
        // Route::get('/getcompanyactivejobs', 'App\Http\Controllers\Api\JobsController@getCompanyActiveJobs');
    });
    Route::get('job/getcompanyactivejobs', 'App\Http\Controllers\Api\JobsController@getCompanyActiveJobs');
});

Route::group(['prefix' => 'admin/settings'], function () {
    Route::put('/updateadminsettings/{id}', [AdminSettingsController::class, 'updateAdminSetting']);
    Route::put('/update-logo-and-favicon', [AdminSettingsController::class, 'updateLogoAndFavicon']);
    //Route::get('/getallnewadminsettings', 'App\Http\Controllers\Api\AdminSettingsController@getAllNewAdminSettings');
    Route::put('/update-admin-settings', [AdminSettingsController::class, 'UpdateAdminSettings']);
    Route::get('/chart-data', [AdminSettingsController::class, 'getAdminChartData']);
});

Route::group(['prefix' => 'settings'], function ($router) {
    Route::get('public', 'App\Http\Controllers\Api\AdminSettingsController@getPublicSettings');
});


// TODO: end points below are pending to verify
Route::group(['prefix' => 'users'], function ($router) {
    //Route::put('/updateuserrole/{id}', 'App\Http\Controllers\Api\UserController@updateUserRole');
    //Route::put('/updateemployeetellusinfo/{id}', 'App\Http\Controllers\Api\UserController@updateEmployeeTellUsInfo');
    //Route::put('/updateusercccountinfo/{id}', 'App\Http\Controllers\Api\UserController@updateUserAccountInfo');

    Route::post('/addteammembers', 'App\Http\Controllers\Api\UserController@addTeamMembers');
    Route::post('/social-data-save', 'App\Http\Controllers\Api\UserController@social_data_save');
    Route::put('/updateShowContact/{id}', 'App\Http\Controllers\Api\UserController@updateShowContact');
    Route::put('/updateShowEmail/{id}', 'App\Http\Controllers\Api\UserController@updateShowEmail');
    Route::put('/updatetwofactorauth/{id}', 'App\Http\Controllers\Api\UserController@update2fa');
    Route::get('/showCompletionPercentage/{userId}', 'App\Http\Controllers\Api\UserController@showCompletionPercentage');
    Route::get('/getActiveEmployer', 'App\Http\Controllers\Api\UserController@getActiveEmployer');
    Route::get('/getActiveEmployee', [UserController::class, 'getActiveEmployee']);
    Route::get('/getInActiveEmployee', 'App\Http\Controllers\Api\UserController@getInActiveEmployee');
    Route::get('/employers', [UserController::class, 'getEmployers']);
    Route::get('/employees', [UserController::class, 'getEmployees']);
    Route::get('/search-current-positions', [UserController::class, 'searchCurrentPositions']);
    Route::get('/employees/filter', [UserController::class, 'getEmployeesFilter']);
    Route::post('/export', [UserController::class, 'userExport']);
    Route::post('/export-employer', [UserController::class, 'employerExport']);
    Route::get('/employees/{id}', [UserController::class, 'showEmployee']);
    //Route::get('/getAllUsers', 'App\Http\Controllers\Api\UserController@getAllUsers');
    Route::get('/getAllinactiveUsers', 'App\Http\Controllers\Api\UserController@getAllinactiveUsers');
    Route::put('/updateUserSocialLinks/{id}', 'App\Http\Controllers\Api\UserController@updateSocialLinks');
    Route::get('/getusersdetails/{id}', 'App\Http\Controllers\Api\UserController@getUserDetails');
    Route::get('/getUserDetailsslug/{id}', 'App\Http\Controllers\Api\UserController@getUserDetailsslug');
    Route::get('/getemployerusersdetails/{id}', 'App\Http\Controllers\Api\UserController@getEmployerUserDetails');
    Route::put('/updatesingleuserbackgroundbannerimage/{id}', 'App\Http\Controllers\Api\UserController@updateSingleUserBackgroundBannerImage');
    Route::put('/updateusercarddetails/{id}', 'App\Http\Controllers\Api\UserController@updateUserCardDetails');
    Route::get('/experiance', 'App\Http\Controllers\Api\UserController@experiance');
    Route::put('/updateUserSectorandIndustry/{id}', 'App\Http\Controllers\Api\UserController@updateUserSectorandIndustry');
    Route::delete('/jobType/{id}', 'App\Http\Controllers\Api\UserController@deleteJobType');
    Route::get('/getuserallteammembers/{id}', 'App\Http\Controllers\Api\UserController@getUserAllTeamMembers');
    Route::get('/getsingleteammember/{id}', 'App\Http\Controllers\Api\UserController@getSingleTeamMember');
    Route::put('/updateteammember/{id}', 'App\Http\Controllers\Api\UserController@updateTeamMember');
    Route::delete('/teammember/{id}', 'App\Http\Controllers\Api\UserController@deleteTeamMember');
    Route::get('/getuserdetailsbyemail', 'App\Http\Controllers\Api\UserController@getUserDetailsByEmail');

    Route::put('/firstlogin/{user_id}', 'App\Http\Controllers\Api\UserController@isFirstLogin');
    Route::put('/updateUnlockInstantApply/{user_id}', 'App\Http\Controllers\Api\UserController@updateUnlockInstantApply');
    Route::get('/jobpreferences/{user_id}', 'App\Http\Controllers\Api\UserController@getJobPreferences');
    Route::get('/getusersdetails/{id}', 'App\Http\Controllers\Api\UserController@getUserDetails');
    Route::post('/employee/{id}', 'App\Http\Controllers\Api\UserController@updateEmployee');
    Route::delete('/user/{id}', 'App\Http\Controllers\Api\UserController@deleteUser');
    Route::post('/delete-bulkusers', 'App\Http\Controllers\Api\UserController@deleteUsers');
    Route::post('/delete-bulkemployers', 'App\Http\Controllers\Api\UserController@deleteEmployers');
    Route::post('/Activateuser/{id}', 'App\Http\Controllers\Api\UserController@activateUser');

    //Route::put('/updatejobstatus/{user_id}/{value}', 'App\Http\Controllers\Api\UserController@updateJobStatus');
    Route::post('/useraccountdetails/{id}', 'App\Http\Controllers\Api\UserController@updateUserAccountDetails');
    Route::post('/uploadAdminLogoUpdate/{id}', 'App\Http\Controllers\Api\UserController@updateAdminLogo');
    Route::post('/changepassword', 'App\Http\Controllers\Api\UserController@changepassowrd');
    Route::put('/updateJobPref/{id}', 'App\Http\Controllers\Api\UserController@updateJobPref');
    Route::get('/getSocialLinks/{id}', 'App\Http\Controllers\Api\UserController@getSocialLinks');

    Route::get('/test-all-users', [UserController::class, 'getTestAllUsers']);


    // /* testimonals */
    // Route::get('/testimonials', [TestimonialController::class, 'index']);
    // Route::post('/testimonials', [TestimonialController::class, 'createOrUpdate']);
    // Route::get('/testimonials/{id}', [TestimonialController::class, 'show']);
    // Route::delete('/testimonials/{id}', [TestimonialController::class, 'destroy']);
});



Route::group(['prefix' => 'testimonials'], function () {
    Route::get('/', [TestimonialController::class, 'index']);
    Route::post('/create', [TestimonialController::class, 'createOrUpdate']);
    Route::get('/show/{id}', [TestimonialController::class, 'show']);
    Route::delete('/delete/{id}', [TestimonialController::class, 'destroy']);
});

Route::group(['prefix' => 'jobs'], function () {
    Route::get('/', [JobsController::class, 'index']);
    Route::get('{id}', [JobsController::class, 'getSingleJob']);
    Route::post('export', [JobsController::class, 'exportJobs']);
});

Route::group(['prefix' => 'collection'], function () {
    Route::get('/by-user', [CollectionController::class, 'getCollectionByUserId']);
    Route::post('/addcollection', [CollectionController::class, 'store']);
    Route::get('/user-collection-data', [CollectionController::class, 'usersCollection']);
});


Route::put('/updatejobstatus/{user_id}/{value}', 'App\Http\Controllers\Api\UserController@updateJobStatus');


Route::post('/forgetpassword', 'App\Http\Controllers\Api\UserController@forgetpassword');
Route::post('/reset-password', 'App\Http\Controllers\Api\UserController@resetPassword');

// Route::post('/updateuseraccountdetails/{id}', 'App\Http\Controllers\Api\UserController@updateUserAccountDetails');

Route::group(['prefix' => 'company'], function ($router) {
    Route::get('getallcompanyandjobs', 'App\Http\Controllers\Api\CompanyController@getAllCompanyAndJobs');
    Route::get('getactivecompanies', 'App\Http\Controllers\Api\CompanyController@getAllActiveCompanies');

    Route::get('{id}', [CompanyController::class, 'show']);
    Route::post('claim-company', [CompanyController::class, 'createOrUpdateCompanyClaimRequest']);
    Route::post('accept-reject-claim-company', [CompanyController::class, 'approveOrRejectClaimRequest']);
    // Company routes

    Route::get('getcompanyfirstinsightschart', 'App\Http\Controllers\Api\CompanyController@getCompanyFirstInsightsChart');
    Route::get('getcompanysecondinsightschart', 'App\Http\Controllers\Api\CompanyController@getCompanySecondInsightsChart');
});

// Route::group(['prefix' => 'company'], function ($router) {

//     Route::get('/companyEntryForm', [CompanyController::class, 'createUpdateCompany']);
//     Route::post('/crate-update-company', [CompanyController::class, 'createOrUpdateCompany']);
// });

Route::group(['prefix' => 'education'], function ($router) {
    // Education routes
    Route::post('createeducation', 'App\Http\Controllers\Api\EducationController@createEducation');
    Route::get('/getsingleeducation/{id}', 'App\Http\Controllers\Api\EducationController@getSingleEducation');
    Route::get('/getsingleeducationid/{id}', 'App\Http\Controllers\Api\EducationController@getSingleEducationbyID');
    Route::put('updateeducation', 'App\Http\Controllers\Api\EducationController@updateEducation');
    Route::delete('/education/{id}', 'App\Http\Controllers\Api\EducationController@deleteEducation');
});

Route::group(['prefix' => 'employers'], function () {
    Route::post('/', [EmployerController::class, 'createEmployer']);
    Route::get('/{id}', [EmployerController::class, 'showEmployer']);
    Route::put('/{id}', [EmployerController::class, 'updateEmployer']);
});

/**
 * @deprecated endpoints group below will be unused
 */

Route::group(['prefix' => 'companyprofileview'], function ($router) {
    Route::get('/getcompanyuserviewedbydays/{user_id}/{days}', 'App\Http\Controllers\Api\CompanyProfileViewController@getCompanyUserViewedByDays');
    Route::post('/createcompanyview', 'App\Http\Controllers\Api\CompanyProfileViewController@insertCompanyView');
    Route::get('/getcompanyProfileviewuserviewcount', 'App\Http\Controllers\Api\CompanyProfileViewController@getCompanyProfileViewUserViewCount');
    Route::get('/getcompanyprofilealluserviewcount', 'App\Http\Controllers\Api\CompanyProfileViewController@getCompanyProfileAllUserViewCount');

    Route::get('/getpopularcompany', 'App\Http\Controllers\Api\CompanyProfileViewController@getPopularCompany');
});

Route::group(['prefix' => 'workexperience'], function ($router) {
    Route::get('/getsingleworkexperience/{id}', 'App\Http\Controllers\Api\WorkExperienceController@getSingleWorkExperience');
    Route::get('/getsingleworkexperienceID/{id}', 'App\Http\Controllers\Api\WorkExperienceController@getSingleWorkExperienceID');
    Route::post('/createworkexperience', 'App\Http\Controllers\Api\WorkExperienceController@createWorkExperience');
    Route::put('updateworkexperience', 'App\Http\Controllers\Api\WorkExperienceController@updateWorkExperience');
    Route::delete('workexperience/{id}', 'App\Http\Controllers\Api\WorkExperienceController@destroyWorkExperience');
});

Route::group(['prefix' => 'applications'], function ($router) {
    Route::get('getallapplications/{id}', 'App\Http\Controllers\Api\ApplicationsController@getAllApplications');
    Route::post('createapplication', 'App\Http\Controllers\Api\ApplicationsController@createApplication');

    Route::get('getTotalApplicationsForjob/{id}', 'App\Http\Controllers\Api\ApplicationsController@getTotalApplicationsForjob');
    Route::get('getTotalSavedjob/{id}', 'App\Http\Controllers\Api\ApplicationsController@getTotalSavedjob');

    Route::get('getalljobapplications', 'App\Http\Controllers\Api\ApplicationsController@getAllJobApplications');
    Route::put('updatehiringstatus/{id}', 'App\Http\Controllers\Api\ApplicationsController@updateHiringStatus');
    Route::get('getsinglejobapplications', 'App\Http\Controllers\Api\ApplicationsController@getSingleJobApplications');

    Route::get('gettotalcompanyjobapplicationscount', 'App\Http\Controllers\Api\ApplicationsController@getTotalCompanyJobApplicationsCount');
});

Route::group(['prefix' => 'companyfollower'], function ($router) {
    Route::get('getcompanyfollowersbycompanyid/{company_id}/', 'App\Http\Controllers\Api\CompanyFollowersController@getCompanyFollowersByCompanyid');
    // Route::post('createcompanyfollower', 'App\Http\Controllers\Api\CompanyFollowersController@createCompanyFollower');
    // Route::delete('deletecompanyfollower/{id}', 'App\Http\Controllers\Api\CompanyFollowersController@deleteCompanyFollower');
});

Route::group(['prefix' => 'companyfollower'], function ($router) {
    Route::get('getsinglecompaniesuserfollow', 'App\Http\Controllers\Api\CompanyFollowersController@getSingleCompaniesUserFollow');
    Route::post('createcompanyfollower', 'App\Http\Controllers\Api\CompanyFollowersController@createCompanyFollower');
    Route::delete('deletecompanyfollower', 'App\Http\Controllers\Api\CompanyFollowersController@deleteCompanyFollower');
    Route::get('getsinglecompanyfollowersbycompanyid/{company_id}/', 'App\Http\Controllers\Api\CompanyFollowersController@getCompanyFollowersByCompanyid');
});

Route::group(['prefix' => 'companyreview'], function ($router) {
    // ----User part---- //
    Route::get('getsinglecompanyreview', 'App\Http\Controllers\Api\CompanyReviewsController@getSingleCompanyReview');
    Route::post('createcompanyreview', 'App\Http\Controllers\Api\CompanyReviewsController@createCompanyReview');
    Route::post('helpful_yes', 'App\Http\Controllers\Api\CompanyReviewsController@helpfulYes');
    Route::post('helpful_no', 'App\Http\Controllers\Api\CompanyReviewsController@helpfulNo');
    Route::post('report_review', 'App\Http\Controllers\Api\CompanyReviewsController@reportReview');
    // Route::delete('deletecompanyfollower', 'App\Http\Controllers\Api\CompanyFollowersController@deleteCompanyFollower');
    // Route::get('getsinglecompanyfollowersbycompanyid/{company_id}/', 'App\Http\Controllers\Api\CompanyFollowersController@getCompanyFollowersByCompanyid');


    // -----Admin part---- //
    Route::post('/approve-remove-review', 'App\Http\Controllers\Api\CompanyReviewsController@approveOrRemoveReview');
    Route::post('/remove-reject-report', 'App\Http\Controllers\Api\CompanyReviewsController@removeOrRejectReport');
    Route::delete('/delete-review/{review_id}', 'App\Http\Controllers\Api\CompanyReviewsController@deleteReview');
    Route::get('/reviews/grouped-by-company', 'App\Http\Controllers\Api\CompanyReviewsController@getReviewsGroupedByCompany');
    Route::get('/reviews/manage-review', 'App\Http\Controllers\Api\CompanyReviewsController@manageSingleCompanyReview');
});

Route::group(['prefix' => 'automation'], function ($router) {
    // ----Admin part---- //
    Route::get('overview-graph-data', 'App\Http\Controllers\Api\AutomationController@showOverview');
    Route::get('datapoint-list', 'App\Http\Controllers\Api\AutomationController@showDataPoints');
    Route::get('contact-list', 'App\Http\Controllers\Api\AutomationController@showContactList');
    Route::get('/export-contacts', [App\Http\Controllers\Api\AutomationController::class, 'exportContacts']);

    ///Workflow
    Route::get('workflow-list', 'App\Http\Controllers\Api\AutomationController@listWorkFlow');
    Route::post('workflow-save', 'App\Http\Controllers\Api\AutomationController@saveWorkFlow');
    Route::get('workflow/{id}', 'App\Http\Controllers\Api\AutomationController@fetchSingleWorkFlow');
    Route::put('workflow/{id}', 'App\Http\Controllers\Api\AutomationController@updateWorkFlow');
    Route::post('workflow/{id}/duplicate', 'App\Http\Controllers\Api\AutomationController@duplicateWorkflow');
    Route::post('workflow-save-draft', 'App\Http\Controllers\Api\AutomationController@saveAsDraft');
    Route::delete('workflow/{id}', 'App\Http\Controllers\Api\AutomationController@deleteWorkFlow');
    Route::patch('workflow/{id}/status', 'App\Http\Controllers\Api\AutomationController@changeWorkFlowStatus');

    ///Email Template
    Route::prefix('email-templates')->group(function () {
        Route::get('/', [AutomationController::class, 'getAllEmailTemplates']); // List all templates
        Route::post('/', [AutomationController::class, 'createEmailTemplate']); // Create new template
        Route::get('/{id}', [AutomationController::class, 'getEmailTemplateById']); // Get template by ID
        Route::put('/{id}', [AutomationController::class, 'updateEmailTemplate']); // Update template
        Route::delete('/{id}', [AutomationController::class, 'deleteEmailTemplate']); // Delete template
    });
});
Route::get('/email/open', [AutomationController::class, 'trackOpen'])->name('email.open');


// Route::group(['prefix' => 'employerreview'], function ($router) {
//     // Route::get('getsinglecompanyreview', 'App\Http\Controllers\Api\CompanyReviewsController@getSingleCompanyReview');
//     Route::post('createemployerreview', 'App\Http\Controllers\Api\EmployerReviewsController@createEmployerReview');
//     // Route::post('helpful_yes', 'App\Http\Controllers\Api\CompanyReviewsController@helpfulYes');
//     // Route::post('helpful_no', 'App\Http\Controllers\Api\CompanyReviewsController@helpfulNo');
//     // Route::post('report_review', 'App\Http\Controllers\Api\CompanyReviewsController@reportReview');
// });

Route::group(['prefix' => 'companysearch'], function ($router) {
    // Route::get('/companies/suggestions', [CompanySearchController::class, 'companySearchSuggestion']);
    // Route::get('/companies/listing-view', [CompanySearchController::class, 'companySearchView']);
    Route::get('/companies', [CompanySearchController::class, 'companySearch']);
    // Route::post('createcompanyreview', 'App\Http\Controllers\Api\CompanyReviewsController@createCompanyReview');
    // Route::post('helpful_yes', 'App\Http\Controllers\Api\CompanyReviewsController@helpfulYes');
    // Route::post('helpful_no', 'App\Http\Controllers\Api\CompanyReviewsController@helpfulNo');
    // Route::post('report_review', 'App\Http\Controllers\Api\CompanyReviewsController@reportReview');
    // Route::delete('deletecompanyfollower', 'App\Http\Controllers\Api\CompanyFollowersController@deleteCompanyFollower');
    // Route::get('getsinglecompanyfollowersbycompanyid/{company_id}/', 'App\Http\Controllers\Api\CompanyFollowersController@getCompanyFollowersByCompanyid');
});

Route::group(['prefix' => 'interview'], function ($router) {
    Route::get('/getallinterviews', 'App\Http\Controllers\Api\InterviewController@getAllInterviews');
    Route::get('/getsingleinterviews/{id}', 'App\Http\Controllers\Api\InterviewController@getSingleInterviews');
    Route::get('/getcompanyuserfutureinterviews/{user_id}', 'App\Http\Controllers\Api\InterviewController@getCompanyUserfutureInterviews');
    Route::post('/insertinterviews', 'App\Http\Controllers\Api\InterviewController@insertInterview');
    Route::put('/updateinterviews/{id}', 'App\Http\Controllers\Api\InterviewController@updateInterview');
    Route::put('/updatezoommeetinglink/{id}', 'App\Http\Controllers\Api\InterviewController@updateZoomMeetingLink');
    Route::delete('/interviews/{id}', 'App\Http\Controllers\Api\InterviewController@deleteInterview');
    Route::get('/getUserInterviewss/{id}', 'App\Http\Controllers\Api\InterviewController@getUserInterviewss');
    Route::put('/updateInterviewStatus/{id}', 'App\Http\Controllers\Api\InterviewController@updateInterviewStatus');

    Route::get('/getapplyjobinterview', 'App\Http\Controllers\Api\InterviewController@getApplyJobInterview');
});

Route::get('/getalljobs', 'App\Http\Controllers\Api\JobsController@getAllJobs');
Route::get('/getalljobssearch', 'App\Http\Controllers\Api\JobsController@getAllJobsSearch');
Route::get('/getalljobswithid/{id}', 'App\Http\Controllers\Api\JobsController@getAllJobsWithId');
Route::get('/checkjobapplied', 'App\Http\Controllers\Api\JobsController@checkJobApplied');

Route::get('/getsinglejobs/{id}', 'App\Http\Controllers\Api\JobsController@getSingleJob');
Route::delete('/jobs/{id}', 'App\Http\Controllers\Api\JobsController@deleteJob');

Route::group(['prefix' => 'jobsview'], function ($router) {
    Route::get('/getjobuserviewedbydays/{user_id}/{days}', 'App\Http\Controllers\Api\JobsViewController@getJobUserViewedByDays');
    Route::post('/createjobview', 'App\Http\Controllers\Api\JobsViewController@insertJobView');
    Route::put('/updatejobview/{id}', 'App\Http\Controllers\Api\JobsViewController@updateJobView');
    Route::get('/getjobviewuserviewcount', 'App\Http\Controllers\Api\JobsViewController@getJobViewUserViewCount');
    Route::get('/getjoballuserviewcount', 'App\Http\Controllers\Api\JobsViewController@getJobAllUserViewCount');
    Route::get('/get-jobview-users', 'App\Http\Controllers\Api\JobsViewController@getJobViewUsers');
});

Route::group(['prefix' => 'skills'], function ($router) {
    Route::get('/getallskills', 'App\Http\Controllers\Api\SkillsController@getAllSkills');
    Route::get('/getsingleskills/{id}', 'App\Http\Controllers\Api\SkillsController@getSingleSkills');
    Route::get('/searchSkills', 'App\Http\Controllers\Api\SkillsController@searchSkills');
    Route::get('/searchallskill', 'App\Http\Controllers\Api\SkillsController@searchAllskill');
    Route::get('/getallskillsforadmin', 'App\Http\Controllers\Api\SkillsController@getAllSkillsForAdmin');
    Route::post('/editandsaveskill', 'App\Http\Controllers\Api\SkillsController@editAndSaveSkill');
    Route::delete('/skill', 'App\Http\Controllers\Api\SkillsController@deleteSkill');
    Route::get('/singleskillbyname', 'App\Http\Controllers\Api\SkillsController@getSingleSkillByName');
});
Route::group(['prefix' => 'skills'], function ($router) {
    Route::get('/getjobsskills', 'App\Http\Controllers\Api\SkillsController@getJobsSkills');
});

Route::group(['prefix' => 'employeeskills'], function ($router) {
    Route::post('/addemployeeskills', 'App\Http\Controllers\Api\EmployeeSkillsController@addEmployeeSkills');
    Route::get('/getSingleUserSkill/{id}', 'App\Http\Controllers\Api\EmployeeSkillsController@getSingleUserSkill');
    Route::delete('/employeeskills/{id}', 'App\Http\Controllers\Api\EmployeeSkillsController@deleteEmployeeSkills');
});

Route::group(['prefix' => 'languages'], function ($router) {
    Route::get('/getalllanguages/{user_id}', 'App\Http\Controllers\Api\LanguagesController@getAllLanguages');
    Route::get('/getsinglelanguages/{id}', 'App\Http\Controllers\Api\LanguagesController@getSingleLanguages');

    Route::get('/getSingleUserLanguage/{id}', 'App\Http\Controllers\Api\LanguagesController@getSingleUserLanguage');

    Route::post('/addlanguage', 'App\Http\Controllers\Api\LanguagesController@addLanguages');
    Route::put('/updatelanguages', 'App\Http\Controllers\Api\LanguagesController@updateLanguages');
    Route::delete('/language/{id}', 'App\Http\Controllers\Api\LanguagesController@deletelanguage');
});

Route::group(['prefix' => 'portfolio'], function ($router) {
    Route::get('/getsingleportfolio/{id}', 'App\Http\Controllers\Api\PortfolioController@getSinglePortfolio');
    Route::get('/getSinglePortfolioid/{id}', 'App\Http\Controllers\Api\PortfolioController@getSinglePortfolioID');
    Route::post('/addportfolio', 'App\Http\Controllers\Api\PortfolioController@addPortfolio');
    //Route::put('/updateportfolio/{id}', 'App\Http\Controllers\Api\PortfolioController@updatePortfolio');
    Route::put('/updateportfolio', 'App\Http\Controllers\Api\PortfolioController@updatePortfolio');
    Route::delete('/portfolio/{id}', 'App\Http\Controllers\Api\PortfolioController@deletePortfolio');
});

Route::group(['prefix' => 'sector'], function ($router) {
    Route::get('/getallsectors', 'App\Http\Controllers\Api\SectorController@getAllSectors');
    Route::get('/getallsectorsforadmin', 'App\Http\Controllers\Api\SectorController@getAllSectorsForAdmin');
    Route::post('/editandsavesector', 'App\Http\Controllers\Api\SectorController@editAndSaveSector');
    Route::delete('/sector', 'App\Http\Controllers\Api\SectorController@deleteSector');
});

Route::group(['prefix' => 'sectors'], function ($router) {
    Route::get('/', [SectorController::class, 'index']);
    Route::get('/getallsectors', 'App\Http\Controllers\Api\SectorController@getAllSectors');
    Route::get('/searchallsectors', 'App\Http\Controllers\Api\SectorController@searchAllSectors');
});

Route::group(['prefix' => 'settings'], function ($router) {
    Route::get('/getuseraccesssettings', 'App\Http\Controllers\Api\SettingsController@getUserAccessSettings');
    Route::put('/updateaccountaccesssettings', 'App\Http\Controllers\Api\SettingsController@updateAccountAccessSettings');
    Route::put('/updatenewsletteraccesssettings', 'App\Http\Controllers\Api\SettingsController@updateNewsLetterAccessSettings');
    Route::put('/updaterecommendationsaccesssettings', 'App\Http\Controllers\Api\SettingsController@updateRecommendationsAccessSettings');
    Route::put('/updateannouncementsaccesssettings', 'App\Http\Controllers\Api\SettingsController@updateAnnouncementsAccessSettings');
    Route::put('/updatemessagefromcandidateaccesssettings', 'App\Http\Controllers\Api\SettingsController@updateMessageFromCandidateAccessSettings');
    Route::post('create', 'App\Http\Controllers\Api\SettingsController@createUserSetting');
});

Route::group(['prefix' => 'payment'], function ($router) {
    Route::get('/getuserpaymentdetails/{user_id}', 'App\Http\Controllers\Api\PaymentController@getUserPaymentDetails');
    Route::post('/savepayment', 'App\Http\Controllers\Api\PaymentController@savePayment');
    Route::get('/getuserallpaymentdetails/{user_id}', 'App\Http\Controllers\Api\PaymentController@getUserAllPaymentDetails');
    Route::get('/getstripeplandetails', 'App\Http\Controllers\Api\PaymentController@getStripePlanDetails');

    Route::get('/getlastpaymentdetails', 'App\Http\Controllers\Api\PaymentController@getLastPaymentdetails');
});

Route::group(['prefix' => 'message'], function ($router) {
    Route::post('/savemessage', 'App\Http\Controllers\Api\MessagesController@saveMessage');
    Route::get('/getallemployerreceivermessages/{user_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployerReceiverMessages');
    Route::get('/getallemployeesreceivermessages/{user_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployeesReceiverMessages');
    Route::get('/getallemployersingleusermessages/{candidate_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployerSingleUserMessage');
    Route::get('/getallemployeessingleusermessages/{candidate_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployeesSingleUserMessage');
    Route::get('/getallreceiverusermessages', 'App\Http\Controllers\Api\MessagesController@getAllReceiverUserMessages');

    Route::put('/updatearchivedmessages', 'App\Http\Controllers\Api\MessagesController@updateArchivedMessages');
    Route::put('/updateunarchivedmessages', 'App\Http\Controllers\Api\MessagesController@updateUnArchivedMessages');

    Route::put('/updatemessagesreadunreadstatus', 'App\Http\Controllers\Api\MessagesController@UpdateMessageReadUnReadStatus');

    Route::get('/getallemployerreceiverarchivedmessages/{user_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployerReceiverArchivedMessages');
    Route::get('/getallemployeesreceiverarchivedmessages/{user_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployeesReceiverArchivedMessages');
    Route::get('/getallemployersingleuserarchivedmessages/{candidate_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployerSingleUserArchivedMessages');
    Route::get('/getallemployeessingleuserarchivedmessages/{candidate_id}', 'App\Http\Controllers\Api\MessagesController@getAllEmployeesSingleUserArchivedMessages');
    Route::get('/getallreceiveruserarchivedmessages', 'App\Http\Controllers\Api\MessagesController@getAllReceiverUserArchivedMessages');

    Route::get('/gettotalmessageunreadcount', 'App\Http\Controllers\Api\MessagesController@getTotalMessageUnReadCount');
    Route::get('/gettotalmessagecount', 'App\Http\Controllers\Api\MessagesController@getFirstMessageCheckCount');
});

Route::group(['prefix' => 'membership'], function ($router) {

    Route::get('/getsinglemembership/{id}', 'App\Http\Controllers\Api\MembershipController@getSingleMembership');
    Route::get('/get-all-memebership-details/{user_id}', 'App\Http\Controllers\Api\MembershipController@getAllMemeberShipDetails');
});

Route::post('/saveresume', 'App\Http\Controllers\Api\ResumesController@saveResume');
Route::get('/getSingleOwnResume/{id}', 'App\Http\Controllers\Api\ResumesController@getSingleOwnResume');
Route::get('/getUserDefaultResume/{id}', 'App\Http\Controllers\Api\ResumesController@getUserDefaultResume');
Route::delete('/resume/{id}', 'App\Http\Controllers\Api\ResumesController@deleteResume');
Route::put('/updateDefaultResume/{id}', 'App\Http\Controllers\Api\ResumesController@updateDefaultResume');

Route::group(['prefix' => 'resume'], function ($router) {
    // Route::post('/saveresume', 'App\Http\Controllers\Api\ResumesController@saveResume');
    Route::get('/getuserresume', 'App\Http\Controllers\Api\ResumesController@getUserResume');
    Route::get('/getuserresumedownload', 'App\Http\Controllers\Api\ResumesController@getDownloadLink');
    Route::delete('/resume/{id}', 'App\Http\Controllers\Api\ResumesController@deleteResume');
});

Route::group(['prefix' => 'countries'], function ($router) {
    Route::get('/', [CountriesController::class, 'index']);
    Route::get('/countries-all', 'App\Http\Controllers\Api\CountriesController@getAllStatusCountries');
    Route::get('/countries', 'App\Http\Controllers\Api\CountriesController@getAllCountries');
    //Route::get('/searchallcountry', 'App\Http\Controllers\Api\CountriesController@searchAllCountry');
    Route::get('/getcountries', 'App\Http\Controllers\Api\CountriesController@getCountries');

    Route::post('/editandsavecountry', 'App\Http\Controllers\Api\CountriesController@editAndSaveCountry');
    Route::delete('/country', 'App\Http\Controllers\Api\CountriesController@deleteCountry');
    Route::get('/getallcountriesforadmin', 'App\Http\Controllers\Api\CountriesController@getAllCountriesForAdmin');

    Route::get('/getcountriesselectedids', 'App\Http\Controllers\Api\CountriesController@getCountriesSeletedIds');
    Route::put('/countrystatus', 'App\Http\Controllers\Api\CountriesController@updateCountryStatus');
    Route::get('/single-country-by-name', 'App\Http\Controllers\Api\CountriesController@getSingleCountryByName');
    Route::get('/nationality', 'App\Http\Controllers\Api\CountriesController@getAllNationality');
    Route::get('/getcountriescities', 'App\Http\Controllers\Api\CountriesController@getAllCountriesCities');
});

Route::group(['prefix' => 'cities'], function ($router) {
    Route::get('/getallcities', 'App\Http\Controllers\Api\CitiesController@getAllCities');
    Route::get('/getsinglecountryallcities/{country_id}', 'App\Http\Controllers\Api\CitiesController@getSingleCountryAllCities');
    Route::get('/getallcitiesbycountryname/{country_name}', 'App\Http\Controllers\Api\CitiesController@getAllCitiesByCountryName');
    Route::get('/searchallcities', 'App\Http\Controllers\Api\CitiesController@searchAllCities');
    Route::post('/editandsavecity', 'App\Http\Controllers\Api\CitiesController@editAndSaveCity');
    Route::delete('/city', 'App\Http\Controllers\Api\CitiesController@deleteCity');
    Route::get('/single-city-by-name', 'App\Http\Controllers\Api\CitiesController@getSingleCityByName');
    Route::get('/getallcitiesforadmin', 'App\Http\Controllers\Api\CitiesController@getAllCitiesForAdmin');
    Route::put('/updatecitystatus', 'App\Http\Controllers\Api\CitiesController@updateCityStatus');
});

Route::get('/send-notification', 'App\Http\Controllers\Api\NotificationController@sendNotification');

Route::group(['prefix' => 'errorlog'], function () {
    Route::get('lists', 'App\Http\Controllers\Api\ErrorLogController@errorLists');
    Route::post('search', 'App\Http\Controllers\Api\ErrorLogController@errorSearch');
    Route::get('detail/{id}', 'App\Http\Controllers\Api\ErrorLogController@geterrorDetails');
});

Route::get('all-error-log', 'App\Http\Controllers\Api\ErrorLogController@errorLists');
Route::delete('error-log', 'App\Http\Controllers\Api\ErrorLogController@deleteerrorlog');
Route::delete('all-error-log', 'App\Http\Controllers\Api\ErrorLogController@deleteAllErorlog');

Route::group(['prefix' => 'industry'], function ($router) {
    Route::get('/getallindustries', 'App\Http\Controllers\Api\IndustriesController@getAllIndustries');
    Route::get('/getallindustriesforadmin', 'App\Http\Controllers\Api\IndustriesController@getAllIndustriesForAdmin');
    Route::post('/editandsaveindustries', 'App\Http\Controllers\Api\IndustriesController@editAndSaveIndustries');
    Route::delete('/industries', 'App\Http\Controllers\Api\IndustriesController@deleteIndustries');
});


// ** Blogs Routes ** //
Route::group(['prefix' => 'blog'], function ($router) {
    Route::get('/get-all-blogs', [App\Http\Controllers\Api\BlogController::class, 'getAllBlogs']);
    Route::post('/edit-and-save-blog-data', [App\Http\Controllers\Api\BlogController::class, 'editAndSaveBlogData']);
    Route::delete('/blog', [App\Http\Controllers\Api\BlogController::class, 'deleteBlog']);
    Route::get('/blogs/{tag}', [App\Http\Controllers\Api\BlogController::class, 'getDataByTag']);
    Route::get('{slug}/content', [App\Http\Controllers\Api\BlogController::class, 'getBlogBySlug']);
    Route::get('/get-blog-category', [App\Http\Controllers\Api\BlogCategoryController::class, 'getAllBlogCategories']);

    Route::get('/get-home-top-blogs', [App\Http\Controllers\Api\BlogController::class, 'getHomeTopBlogs']);
});

Route::group(['prefix' => 'subscribe'], function ($router) {
    Route::post('/subscribe', [App\Http\Controllers\Api\MailchimpController::class, 'subscribe'])->name('subscribe');
});


Route::group(['prefix' => 'candidate'], function ($router) {
    Route::get('/getallcandidatesearch', 'App\Http\Controllers\Api\CandidatesFilterController@getAllCandidateSearch');
    Route::get('/getallcandidatefilter/{user_id}/{search_keyword}', 'App\Http\Controllers\Api\CandidatesFilterController@getAllCandidateFilter');
    Route::put('updatesavecandidatefilter', 'App\Http\Controllers\Api\CandidatesFilterController@UpdateSaveCandidateFilter');
    Route::delete('candidatefilter', 'App\Http\Controllers\Api\CandidatesFilterController@DeleteCandidateFilter');
    Route::post('checkandupdateresumesviewed', 'App\Http\Controllers\Api\CandidatesFilterController@checkAndUpdateResumesViewed');
    Route::post('addupdatestaffmember', 'App\Http\Controllers\Api\CandidatesFilterController@AddUpdateStaffMember');
    Route::get('/getallstaffmembers/{user_id}', 'App\Http\Controllers\Api\CandidatesFilterController@getAllStaffMembers');
    Route::delete('deletestaff', 'App\Http\Controllers\Api\CandidatesFilterController@DeleteStaff');
    Route::get('getallcandidateheadersearch', 'App\Http\Controllers\Api\CandidatesFilterController@getAllCandidateHeaderSearch');
});

Route::group(['prefix' => 'staff'], function ($router) {
    Route::get('/getcurrentstaffalljobs', 'App\Http\Controllers\Api\StaffController@getStaffUserAllJobs');
    Route::put('/updatejobsbystaff/{id}', 'App\Http\Controllers\Api\StaffController@updateJobByStaff');
});

Route::group(['prefix' => 'admin'], function ($router) {
    Route::get('get-staff-member/{id}', 'App\Http\Controllers\Api\UserController@get_staff_member');
    Route::get('get-single-staff-member/{id}', 'App\Http\Controllers\Api\UserController@get_single_staff_member');
    Route::put('update-single-staff-member/{id}', 'App\Http\Controllers\Api\UserController@update_single_staff_member');
});

Route::get('/get-all-dbStrucure', 'App\Http\Controllers\Api\ErrorLogController@getDbStructure');
Route::put('/upload-env', 'App\Http\Controllers\Api\ErrorLogController@uploadEnvFile');
Route::get('download-env', 'App\Http\Controllers\Api\ErrorLogController@downlaodenv');

Route::get('/migrate', [MigrationController::class, 'migrateFields']);
Route::get('/companyslugupdate', function () {
    $company = DB::table('company')
        ->where('company_slug', null)
        ->get();
    foreach ($company as $companies) {
        $slug = Str::slug($companies->company_name, '-');
        DB::table('company')->where('id', $companies->id)->update(['company_slug' => $slug]);
    }
});
Route::get('/companyuseridupdate', function () {
    $company = DB::table('company')
        ->where('user_id', null)
        ->orWhere('user_id', '=', '0')
        ->get();
    if ($company) {
        echo count($company);
        echo '<pre>';
        print_r($company);
        echo '</pre>';
    }
    foreach ($company as $company_data) {
        $user = DB::table('users')->where('created_at', '=', $company_data->created_at)->first();
        if ($user) {
            DB::table('company')->where('id', $company_data->id)->update(['user_id' => $user->id]);
            echo '<pre>';
            print_r($user);
            echo '</pre>';
        }
    }
    //DB::table('company')->where('id', '1125')->update(['user_id' => '14296']);
});
Route::get('/candidateslugupdate', function () {
    $candidateUsers = DB::table('users')
        ->where('role', 'employee')
        ->where('slug', null)
        ->get();
    foreach ($candidateUsers as $candidateUser) {
        $slug = Str::slug($candidateUser->name, '-');
        DB::table('users')->where('id', $candidateUser->id)->update(['slug' => $slug]);
    }
    print_r($candidateUsers->count());
});
Route::get('/company-meta-title-desc-update', function () {
    $activecompanies = DB::table('company')
        ->where('status', 'active')
        ->get();

    foreach ($activecompanies as $activecompany) {
        $location = DB::table('countries')->where('id', $activecompany->company_location)->first();
        if ($location) {
            $company_meta_tag = $activecompany->company_name . ' Careers - The Talent Point';
            $company_meta_desc = 'Apply for jobs by ' . $activecompany->company_name . '. Register for Free & Search job openings at ' . $activecompany->company_name . '. Register for Free and Explore Careers in ' . $location->country_name . '.';
        } else {
            $company_meta_tag = $activecompany->company_name . ' Careers - The Talent Point';
            $company_meta_desc = 'Apply for jobs by ' . $activecompany->company_name . '. Register for Free & Search job openings at ' . $activecompany->company_name . '. Register for Free and Explore Careers.';
        }
        $updatecompany[] = DB::table('company')->where('id', $activecompany->id)->update(['meta_tag' => $company_meta_tag, 'meta_desc' => $company_meta_desc]);
    }
    echo count($updatecompany) . ' active companies updated successfully';
    exit;
});
Route::get('/activecompanies', function () {
    $activecompanies = DB::table('company')
        ->where('status', 'active')
        ->get();

    foreach ($activecompanies as $activecompany) {
        echo 'Active Company ID - ' . $activecompany->id;
        echo 'Active Company Name - ' . $activecompany->company_name;
        echo 'Active Company Slug - ' . $activecompany->company_slug . '</br>';
    }
});
Route::group(['prefix' => 'salaries'], base_path('routes/modules/salaries.php'));
