<?php

namespace App\Http\Controllers\Api;

use App\Models\Membership;
use App\Models\Plan;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Src\AppFramework\ApiController;
use Src\Authentication\Application\AuthenticateUserService;
use Src\Authentication\Application\LogoutUserUseCase;
use Src\Authentication\Application\SocialAuthenticateUserService;
use Src\Authentication\Infrastructure\Resources\AuthUserResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendOtpMail;
use App\Mail\RegistrationMail;
use Illuminate\Validation\ValidationException;

/**
 * @group Authentication
 */

class AuthController extends ApiController
{

    /**
     * @param Request $request
     * @return JsonResponse
     */

    /// use for login ///
    public function authenticate(Request $request): JsonResponse
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        try {
            $h = (new AuthenticateUserService)($credentials['email'], $credentials['password']);
            $user = DB::table('users')->where('email', $request->email)->first();
            if ($user->role == "admin") {
            }

            if ($user->is2FA === 1) {
                $otpLength = 4;
                $otp = random_int(pow(10, $otpLength - 1), pow(10, $otpLength) - 1);
                //$otp = 4321;
                DB::table('users')->where('email', $request->email)->update(['otp' => $otp]);
                Mail::to($request->email)->send(new SendOtpMail($user->name, $otp));
                $token = $this->respondWithSuccess(['token' => $h]);
                return response()->json([
                    'status' => true,
                    'message' => 'Please enter the OTP sent to your email for verification.',
                    'user' => $user,
                    'otp' => $otp,
//                    'token' => $user->role == "admin" ? $token : $h
                    'token' => $token
                ]);
            }

            if ($user->role == "employee") {
                DB::table('users')
                    ->where('email', $request->email)
                    ->update(['last_login' => Carbon::now()]);
            }
            return $this->respondWithSuccess(['token' => $h]);
        } catch (Exception $e) {
            return $this->respondForbidden($e->getMessage());
            //return $this->respondError($e->getMessage());
        }
    }

    public function socialAuthenticate(Request $request): JsonResponse
    {

        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'token' => ['required'],
        ]);
        try {
            $h = (new SocialAuthenticateUserService)(
                $credentials['email'],
                $request->get('name'),
                $credentials['token'],
                $request->get('image'),
            );
            return $this->respondWithSuccess(['token' => $h]);
        } catch (Exception $e) {
            return $this->respondForbidden($e->getMessage());
        }
    }

    public function updateNumber(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'number' => 'required',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors(),
                ], 422);
            }
            DB::table('users')->where('id', $request->id)->update(['contact_no' => $request->number]);
            // Create the user
            User::where('id', $request->id)->update([
                'role' => $request->role,
            ]);
            // DB::table('users')->insert([
            //     'role' => $request->role
            // ]);
            return response()->json([
                'status' => true,
                'message' => 'Contact Number Saved Successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function updateRole(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required',
                'role' => 'required'
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $userId = $request->user_id;

            User::where('id', $userId)->update([
                'role' => $request->role,
            ]);

            $user = User::where('id', $userId)->get();

            return response()->json([
                'data' => $user,
                'status' => true,
                'message' => 'User Role Saved Successfully'
            ]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    /**
     * @param Request $request
     * @return JsonResponse
     * @deprecated Use authenticate instead
     */
    public function login(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|string|email',
                'password' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $credentials = $request->only('email', 'password');
            $user = DB::table('users')->where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Email does not exist',
                ], 200);
            }

            if ($user->status == 'pending') {
                return response()->json([
                    'status' => false,
                    'message' => 'Your account is not active. Please activate your account!',
                ], 200);
            }

            if ($user->status == 'deleted') {
                return response()->json([
                    'status' => false,
                    'message' => 'Your account has been deleted. Please contact support for assistance.',
                ], 200);
            }


            if ($user->status == 'deactive') {
                return response()->json([
                    'status' => false,
                    'message' => 'Your account is not active. Please contact support for assistance.',
                ], 200);
            }


            if (!Hash::check($request->password, $user->password)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Incorrect password',
                ], 200);
            }


            if (!Auth::attempt($credentials)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Incorrect credentials',
                ], 200);
            }


            if ($user->is2FA === 1) {
                $otpLength = 4;
                $otp = random_int(pow(10, $otpLength - 1), pow(10, $otpLength) - 1);
                DB::table('users')->where('email', $request->email)->update(['otp' => $otp]);

                Mail::to($request->email)->send(new SendOtpMail($user->name, $otp));

                return response()->json([
                    'status' => true,
                    'message' => 'Please enter the OTP sent to your email for verification.',
                    'user' => $user,
                    'otp' => $otp
                ]);
            }
            // Update login count
            //DB::table('users')->where('id', $user->id)->increment('login_count');
            DB::table('users')->where('id', $user->id)->update(['login_count' => $user->login_count + 1]);
            return response()->json([
                'status' => true,
                'user' => $user,
                // 'authorisation' => [
                //     $this->respondWithToken($token)
                // ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
            ], 500);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @authenticated
     */
    public function matchOTP(Request $request)
    {
        $valdiation = $request->validate([
            'otp' => 'required'
        ]);

        $user = User::find($request->input('user_id'));
        if (!$user) {
            return response()->json([
                'status' => false,
                'message' => 'User not found',
            ]);
        }

        $currentDay = now()->day;
        $currentMonth = now()->month;

        $combinedOTP = intval(str_pad($currentDay, 2, '0', STR_PAD_LEFT) . str_pad($currentMonth, 2, '0', STR_PAD_LEFT));


//        if ($user->otp == $request->input('otp') || $combinedOTP == $request->input('otp')) {
        if ($user->otp == $request->input('otp')) {
            $user->otp = null;
            $user->save();
            return response()->json([
                'status' => true,
                'message' => 'OTP matched successfully',
                'id' => $user->id,
                'otp_required' => $user->otp_required,
            ]);
        } else {

            return response()->json([
                'status' => false,
                'message' => 'Incorrect OTP',
            ]);
        }
    }

    public function register(Request $request): JsonResponse
    {
        try {

            // Define validation rules
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email|max:255', // Check for unique email
            ], [
                'email.unique' => 'The email address is already registered. Please use a different email.', // Custom message
            ]);

            $slug = $this->generateUniqueSlug($request->name);
            $user = (new User)->create([
                'name' => $request->get('name'),
                'email' => $request->get('email'),
                'slug' => $slug

            ]);

            // Mail::to($request->email)->send(new RegistrationMail($user->name, $otp));

            // Mail::to($user->email)
            //     ->bcc(config('mail.from.address'))
            //     ->send(new RegistrationMail($user->name));

            // $notification_text = 'New User registered successfully!';
            // $link = null;
            // $type = 'register';
            // $admin_id = '1';
            // app('App\Http\Controllers\Api\NotificationController')->saveNotification($user->id, $admin_id, $notification_text, $link, $type);
            $token = $user->createToken('signup')->plainTextToken;
            return $this->respondWithSuccess(['token' => $token]);
        }catch (ValidationException $e) {
            // Return validation errors as a JSON response
            return response()->json([
                'success' => false,
                'error' => $e->validator->errors()->first(),
            ], 422);
        } catch (Exception $e) {
            //throw new HttpException(500, $e->getMessage());
            if ($e->getCode() === '23000') {
                return $this->respondError('The email is already registered');
            } else {
                return $this->respondError($e->getMessage());
            }
        }
    }

    private function generateUniqueSlug($name)
    {
        $slug = Str::slug($name);
        $count = 1;


        while (User::where('slug', $slug)->exists()) {
            $slug = Str::slug($name) . '_' . $count;
            $count++;
        }

        return $slug;
    }

    /**
     * Get session
     *
     * Returns the current session
     *
     * @return JsonResponse
     * @authenticated
     */
    public function getSession(): JsonResponse
    {
        if (Auth::check()) {
            $user = User::where('id', Auth::user()->id)->with('profile', 'company')->first();
            return $this->respondWithSuccess(new AuthUserResource($user));
        } else {
            return $this->respondUnAuthenticated();
        }
    }

    /**
     * Logout
     *
     * @return JsonResponse
     * @authenticated
     */
    public function logout(): JsonResponse
    {
        try {
            (new LogoutUserUseCase)();
            return $this->respondWithSuccess();
        } catch (Exception $e) {
            return $this->respondFailedValidation($e->getMessage());
        }
    }

    public function refresh()
    {
        return response()->json([
            'status' => 'success',
            'user' => Auth::user(),
            'authorisation' => [
                'token' => Auth::refresh(),
                'type' => 'bearer',
            ]
        ]);
    }

    protected function respondWithToken(string $token): JsonResponse
    {
        return response()->json([
            'token' => $token,
            'type' => 'bearer',
            //'expires_in' => config('jwt.ttl') * 60,
        ]);
    }

    public function purchasePlan(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'plan_id' => 'required|in:0,1,2',
            'company_id' => 'required|exists:companies,id',
        ]);

        // if ($validator->fails()) {
        //     return response()->json(['error' => $validator->errors()], 422);
        // }

        $userId = $request->input('user_id');
        $planId = $request->input('plan_id');
        $companyId = $request->input('company_id');

        $user = User::find($userId);
        $plan = Plan::find($planId);

        if (!$user || !$plan) {
            return response()->json(['error' => 'User or Plan not found'], 404);
        }

        $membership = Membership::where(['user_id' => $userId, 'status' => 'pending'])->first();

        if (!$membership) {
            $membership = new Membership;
        }

        $membership->fill([
            'user_id' => $userId,
            'plan_id' => $planId,
            'company_id' => $companyId,
            'status' => 'pending',
        ]);

        $membership->save();

        return response()->json([
            'success' => true,
            'message' => 'Membership details updated. Proceed to payment.',
            'membership' => $membership,
        ], 201);
    }
}
