<?php

namespace App\Http\Controllers\Api;

use App\Models\ClaimCompany;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Src\AppFramework\ApiController;
use Src\EmployerManagement\Infrastructure\Resources\CompanyResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use App\Models\Company;
use App\Models\Country;
use App\Models\Job;
use App\Models\Membership;
use App\Models\Applications;
use App\Models\User;
use App\Models\CompanyProfileView;
use App\Models\CompanyFollower;
use App\Models\CompanyReview;
use Illuminate\Support\Str;
use Carbon\Carbon;

class CompanyController extends ApiController
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    /**
     * Get all companies
     *
     * @return JsonResponse
     */
    public function getAllCompany(Request $request)
    {
        try {
            $search = $request->get('search');
            $companies = (new Company)
                ->where('status', 'active')
                ->orderBy('id', 'DESC');

            if ($search) {
                $companies->where('company_name', 'like', '%' . $search . '%');
            }
            return response()->json(['status' => 'success', 'data' => $companies->paginate(10)]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    /**
     * Get a single company by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getSingleCompanyDetails($user_id)
    {
        try {
//            $company = Company::with('logo')->where('user_id', $user_id)->where('status', 'active')->first();
//            if (!$company) {
//                return response()->json(['status' => false, 'message' => 'Company not found']);
//            }

            // Retrieve the user by user_id
            $user = User::find($user_id);

            // Check if the user exists
            if (!$user) {
                return response()->json(['status' => false, 'message' => 'User  not found']);
            }

            $company = $user->company;

            if (!$company) {
                return response()->json(['status' => false, 'message' => 'Company not found']);
            }

            // Assign user's email and contact number to the company
            $company->company_email = $user->email;
            $company->company_contact_no = $user->contact_no;

            if ($company->available_resume_count && (int)$company->available_resume_count === 100000)
            {
                $company->available_resume_count = 'Unlimited';
            }

            return response()->json(['status' => true, 'data' => $company]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    /**
     * Create a new company.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createCompany(Request $request)
    {

        try {
            // $authUser = (new User)->find(Auth::id());
            $company = new Company;
            // $company->user_id = $authUser->id;
            $company->user_id = $request->user_id;
            $company->company_name = $request->company_name;
            $company->company_slug = Str::slug($request->company_name, '-');
            $company->designation = $request->designation;
            $company->company_website = null;
            $company->company_location = null;
            $company->company_sector = null;
            $company->no_of_employees = null;
            $company->company_description = null;
            $company->company_logo = null;
            $company->meta_tag = $request->company_name . ' Careers - The Talent Point';
            $company->meta_desc = 'Apply for jobs by ' . $request->company_name . '. Register for Free & Search job openings at ' . $request->company_name . '. Register for Free and Explore Careers.';
            $company->company_contact_no = null;
            $company->fk_logo_file_uuid = $request->fk_logo_file_uuid;
//            $company->available_resume_count = $request->available_resume_count ? $request->available_resume_count : null;
            $company->available_resume_count = 30;
            $company->save();

            User::where('id', $request->user_id)->update(['company_id' => $company->id]);

            $user = Membership::firstOrCreate(
                ['user_id' => $request->user_id],
                [
                    'company_id' => $company->id,
                    'plan_id' => 1,
                    'expire_at' => Carbon::now()->addDay()->toDateString(),
                    'purchase_at' => Carbon::now()->toDateString(),
                    'status' => 'active'
                ]
            );

            return response()->json(['status' => true, 'message' => 'Company created successfully', 'data' => $company], 201);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    /**
     * Update an existing company.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateCompany(Request $request, $id)
    {
        try {
            $company = Company::where('id', $id)->first();
            $location = Country::where('id', $request->edit_company_location)->first();
            if (!$company) {
                return response()->json(['status' => false, 'message' => 'Company not found'], 404);
            }
            $company->company_name = $request->edit_company_name;
            $company->company_email = $request->edit_company_email;
            $company->designation = $request->edit_designation;
            $company->company_website = $request->edit_company_website;
            $company->company_location = $request->edit_company_location;
            $company->company_sector = $request->edit_company_sector;
            $company->no_of_employees = $request->edit_no_of_employees;
            $company->company_description = $request->edit_company_description;
            $company->company_contact_no = $request->edit_company_contact_no;
            if ($request->hasFile('edit_company_logo')) {
                $image = $request->file('edit_company_logo');
                $fileName = time() . '.' . $image->getClientOriginalExtension();
                $img = Image::make($image->getRealPath());
                $img->stream();
                Storage::disk('public')->put('images/companylogo/' . $fileName, $img);
                $company->company_logo = $fileName;

                // $randomNumber = mt_rand(1000000000, 9999999999);
                // $imagePath = $request->file('edit_company_logo');
                // $imageName = $randomNumber . $imagePath->getClientOriginalName();
                // $imagePath->move(public_path('images/companylogo'), $imageName);
                // $company->company_logo = $imageName;
            }
            //$company->available_resume_count = $request->edit_available_resume_count;
            //$company->status = $request->status;
            $company->meta_tag = $company->company_name . ' Careers - The Talent Point';
            $company->meta_desc = 'Apply for jobs by ' . $company->company_name . '. Register for Free & Search job openings at ' . $company->company_name . '. Register for Free and Explore Careers in ' . $location->country_name . '.';
            $company->save();

            return response()->json(['status' => true, 'message' => 'Company updated successfully', 'data' => $company], 200);
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
            ], 500);
        }
    }

    /**
     * Delete an existing company.
     *
     * @param int $id
     * @return JsonResponse
     */

    public function updateSetCompanyInfo(Request $request, $id)
    {
        try {
            $company = Company::where('id', $id)->first();
            $location = Country::where('id', $request->edit_location)->first();
            if (!$company) {
                return response()->json(['message' => 'Company not found'], 404);
            }
            $company->company_website = $request->edit_website;
            $company->company_location = $request->edit_location;
            $company->company_sector = $request->edit_sector;
            $company->no_of_employees = $request->edit_no_of_employees;
            $company->company_description = $request->edit_description;
            $company->company_contact_no = $request->edit_contact_no;
            $company->meta_tag = $company->company_name . ' Careers - The Talent Point';
            $company->meta_desc = 'Apply for jobs by ' . $company->company_name . '. Register for Free & Search job openings at ' . $company->company_name . '. Register for Free and Explore Careers in ' . $location->country_name . '.';
            //$company->meta_desc = isset($request->edit_description) ? strip_tags(trim($request->edit_description)) : $company->meta_desc;
            $company->save();
            return response()->json(['status' => true, 'message' => 'Company updated successfully', 'data' => $company], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    // public function show(Request $request, $id): JsonResponse
    // {
    //     try {
    //         $company = (new Company)
    //             ->with('logo', 'sector', 'location', 'jobs')
    //             ->where('company_slug', $id)
    //             ->orWhere('id', $id)
    //             ->first();
    //         if (!$company) {
    //             return response()->json(['success' => false, 'message' => 'Company not found', 'data' => []], 404);
    //         }
    //         return $this->respondWithSuccess(new CompanyResource($company));
    //     } catch (Exception $e) {
    //         return $this->respondError($e->getMessage());
    //     }
    // }

    public function show(Request $request, $id): JsonResponse
    {
        try {
            $user_id = $request->user_id;

            // Fetch the company details with related data
            $company = Company::with(['logo', 'sector', 'location', 'jobs'])
                ->where('company_slug', $id)
                ->orWhere('id', $id)
                ->first();
            // dd($company);
            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'Company not found',
                    'data' => []
                ], 404);
            }

            $userClaimedReviews = ClaimCompany::where('user_id', $user_id)->pluck('company_id')->toArray();
            $userFollowedReviews = CompanyFollower::where('user_id', $user_id)->pluck('company_id')->toArray();

            // Fetch the 3 most recent reviews for this company with user details
            $recentReviews = CompanyReview::where('company_id', $company->id)
                ->with('user:id,current_position,countries') // Include user details for each review
                ->orderBy('created_at', 'desc')
                ->take(3)
                ->get();

            // Prepare the response structure
            $response = [
                'company' => new CompanyResource($company), // Company data formatted with CompanyResource
                'claim_flag' => in_array($id, $userClaimedReviews),
                'follow_flag' => in_array($id, $userFollowedReviews),
                'recent_reviews' => $recentReviews->map(function ($review) {
                    return [
                        'id' => $review->id,
                        'rating' => $review->rating,
                        'review_title' => $review->review_title,
                        'review_description' => $review->review_description,
                        'helpful_yes_count' => $review->helpful_yes_count,
                        'helpful_no_count' => $review->helpful_no_count,
                        'created_at' => $review->created_at,
                        'user' => [
                            'id' => $review->user->id,
                            'current_position' => $review->user->current_position,
                            'country' => $review->user->country,
                        ]
                    ];
                })
            ];

            return response()->json([
                'success' => true,
                'message' => 'Company details retrieved successfully',
                'data' => $response
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the company details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateCompanySocialLinks(Request $request, $id)
    {
        try {
            $company = Company::where('id', $id)->first();
            if (!$company) {
                return response()->json(['message' => 'Company not found'], 404);
            }
            $company->linkedin_link = $request->edit_linkedin_link;
            $company->twitter_link = $request->edit_twitter_link;
            $company->instagram_link = $request->edit_instagram_link;
            $company->facebook_link = $request->edit_facebook_link;
            $company->save();
            return response()->json(['status' => true, 'message' => 'Social links updated successfully', 'data' => $company], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateCompanyLogo(Request $request)
    {
        try {
            $validator = Validator::make(
                $request->all(),
                [
                    'company_id' => 'required|exists:company,id',
                    'fk_logo_file_uuid' => 'required|uuid',
                ],
                [
                    'company_id.required' => 'Company ID is required.',
                    'company_id.exists' => 'Company not found.',
                    'fk_logo_file_uuid.required' => 'Logo UUID is required.',
                    'fk_logo_file_uuid.uuid' => 'Invalid UUID format for logo.',
                ]
            );

            if ($validator->fails()) {
                return response()->json([
                    'errors' => $validator->errors(),
                    'status' => false,
                ], 200);
            }

            $company = Company::where('id', $request->company_id)->first();

            if (!$company) {
                return response()->json(['message' => 'Company not found'], 404);
            }

            $company->fk_logo_file_uuid = $request->fk_logo_file_uuid;
            $company->save();

            return response()->json([
                'status' => true,
                'message' => 'Company Logo updated successfully',
                'data' => $company
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    function updateSingleCompanyBackgroundBannerImage(Request $request)
    {
        try {
            $company = Company::find($request->company_id);
            $validator = Validator::make(
                request()->all(),
                [
                    'company_background_banner_img' => 'required|mimes:png,jpg,jpeg|max:1024',
                ],
                [
                    'company_background_banner_img.required' => 'Please select any banner image.',
                    'company_background_banner_img.mimes' => 'Only Allowed file type: png, jpg, jpeg.',
                    'company_background_banner_img.max' => 'Banner image not allowed greater than 1MB.',
                ]
            );
            if ($validator->fails()) {
                return response()->json([
                    'errors' => $validator->errors(),
                    'status' => false,
                ], 200);
            }
            if ($request->hasFile('company_background_banner_img')) {
                $image = $request->file('company_background_banner_img');
                $fileName = time() . '.' . $image->getClientOriginalExtension();
                $img = Image::make($image->getRealPath());
                $img->resize(1323, 227);
                $img->stream();
                Storage::disk('public')->put('images/companybannerImage/' . $fileName, $img);
                $company->background_banner_image = $fileName;

                // $randomNumber = mt_rand(1000000000, 9999999999);
                // $imagePath = $request->file('company_background_banner_img');
                // $imageName = $randomNumber . $imagePath->getClientOriginalName();
                // $imagePath->move(public_path('images/companybannerImage'), $imageName);
                // $company->background_banner_image = $imageName;
            }
            $company->save();
            return response()->json(['message' => 'background banner image updated successfully', 'status' => true], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllCompanyAndJobs(Request $request)
    {
        try {
            $value = $request->value;

            $companies = Company::select('company_name', 'company_slug')
                ->where('company_name', 'like', '%' . $value . '%')
                ->get();

            $jobs = Job::select('job_slug', 'job_title')
                ->where('job_title', 'like', '%' . $value . '%')
                ->get();

            $employee = User::select('name', 'slug')
                ->where('name', 'like', '%' . $value . '%')
                ->get();

            return response()->json(['status' => true, 'companies' => $companies, 'jobs' => $jobs, 'employee' => $employee]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getCompanyFirstInsightsChart(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $company_id = $request->company_id;
            $first_section_time_type = $request->first_section_time_type;
            if ($first_section_time_type) {
                if ($first_section_time_type == '12_months') {
                    $time_type = 'month';
                    $time_val = '12';
                } elseif ($first_section_time_type == '6_months') {
                    $time_type = 'month';
                    $time_val = '6';
                } elseif ($first_section_time_type == '30_days') {
                    $time_type = 'days';
                    $time_val = '30';
                } elseif ($first_section_time_type == '7_days') {
                    $time_type = 'days';
                    $time_val = '7';
                } else {
                    $time_type = '';
                    $time_val = '';
                }
                if ($time_type == 'month') {
                    $dateS = Carbon::now()->startOfMonth()->subMonth($time_val);
                    $dateE = Carbon::now()->startOfMonth();
                    $company_profile_views_count = CompanyProfileView::whereBetween('created_at', [$dateS, $dateE])
                        ->where('company_id', $company_id)
                        ->where('status', 'active')
                        ->count();

                    $active_jobs_count = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                        ->join('countries', 'jobs.job_country', '=', 'countries.id')
                        ->join('company', 'jobs.company_id', '=', 'company.id')
                        ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                        ->whereBetween('jobs.created_at', [$dateS, $dateE])
                        ->where('jobs.company_id', $company_id)
                        ->where('jobs.job_status', '=', 'active')
                        ->orderByDesc('id')
                        ->count();

                    $company_followers = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->whereBetween('company_followers.created_at', [$dateS, $dateE])->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->get();

                    $company_followers_count = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->whereBetween('company_followers.created_at', [$dateS, $dateE])->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->count();

                    $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                    $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                    $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                    $query->join('users', 'applications.user_id', '=', 'users.id');
                    $query->join('company', 'jobs.company_id', '=', 'company.id');
                    $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                    $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    });
                    $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                    $query->whereBetween('applications.created_at', [$dateS, $dateE]);
                    $query->where('jobs.user_id', $user_id);
                    $query->where('applications.status', 'active');
                    $totalApplications = $query->count();

                    $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                    $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                    $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                    $query->join('users', 'applications.user_id', '=', 'users.id');
                    $query->join('company', 'jobs.company_id', '=', 'company.id');
                    $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                    $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    });
                    $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                    $query->whereBetween('applications.created_at', [$dateS, $dateE]);
                    $query->where('jobs.user_id', $user_id);
                    $query->where('applications.status', 'active');
                    $query->where('applications.hiring_status', 'Yes');
                    $totalShortlistedApplications = $query->count();

                    $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                    $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                    $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                    $query->join('users', 'applications.user_id', '=', 'users.id');
                    $query->join('company', 'jobs.company_id', '=', 'company.id');
                    $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                    $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    });
                    $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                    $query->whereBetween('applications.created_at', [$dateS, $dateE]);
                    $query->where('jobs.user_id', $user_id);
                    $query->where('applications.status', 'active');
                    $query->where('applications.hiring_status', 'No');
                    $totalRejectedApplications = $query->count();
                } else {
                    $company_profile_views_count = CompanyProfileView::whereDate('created_at', '>=', now()->subDays($time_val))
                        ->where('company_id', $company_id)
                        ->where('status', 'active')
                        ->count();

                    $active_jobs_count = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                        ->join('countries', 'jobs.job_country', '=', 'countries.id')
                        ->join('company', 'jobs.company_id', '=', 'company.id')
                        ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                        ->whereDate('jobs.created_at', '>=', now()->subDays($time_val))
                        ->where('jobs.company_id', $company_id)
                        ->where('jobs.job_status', '=', 'active')
                        ->orderByDesc('id')
                        ->count();

                    $company_followers = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->whereDate('company_followers.created_at', '>=', now()->subDays($time_val))->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->get();

                    $company_followers_count = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->whereDate('company_followers.created_at', '>=', now()->subDays($time_val))->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->count();

                    $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                    $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                    $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                    $query->join('users', 'applications.user_id', '=', 'users.id');
                    $query->join('company', 'jobs.company_id', '=', 'company.id');
                    $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                    $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    });
                    $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                    $query->whereDate('applications.created_at', '>=', now()->subDays($time_val));
                    $query->where('jobs.user_id', $user_id);
                    $query->where('applications.status', 'active');
                    $totalApplications = $query->count();

                    $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                    $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                    $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                    $query->join('users', 'applications.user_id', '=', 'users.id');
                    $query->join('company', 'jobs.company_id', '=', 'company.id');
                    $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                    $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    });
                    $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                    $query->whereDate('applications.created_at', '>=', now()->subDays($time_val));
                    $query->where('jobs.user_id', $user_id);
                    $query->where('applications.status', 'active');
                    $query->where('applications.hiring_status', 'Yes');
                    $totalShortlistedApplications = $query->count();

                    $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                    $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                    $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                    $query->join('users', 'applications.user_id', '=', 'users.id');
                    $query->join('company', 'jobs.company_id', '=', 'company.id');
                    $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                    $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    });
                    $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                    $query->whereDate('applications.created_at', '>=', now()->subDays($time_val));
                    $query->where('jobs.user_id', $user_id);
                    $query->where('applications.status', 'active');
                    $query->where('applications.hiring_status', 'No');
                    $totalRejectedApplications = $query->count();
                }
            } else {
                $company_profile_views_count = CompanyProfileView::where('company_id', $company_id)
                    ->where('status', 'active')
                    ->count();

                $active_jobs_count = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                    ->where('jobs.company_id', $company_id)
                    ->where('jobs.job_status', '=', 'active')
                    ->orderByDesc('id')
                    ->count();

                $company_followers = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->get();

                $company_followers_count = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')->join('users', 'company_followers.user_id', '=', 'users.id')->join('company', 'company_followers.company_id', '=', 'company.id')->where('company_followers.company_id', $company_id)->where('company_followers.status', 'active')->count();

                $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                $query->join('users', 'applications.user_id', '=', 'users.id');
                $query->join('company', 'jobs.company_id', '=', 'company.id');
                $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                    $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                        ->where('resumes_viewed.company_id', '=', $company_id);
                });
                $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                $query->where('jobs.user_id', $user_id);
                $query->where('applications.status', 'active');
                $totalApplications = $query->count();

                $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                $query->join('users', 'applications.user_id', '=', 'users.id');
                $query->join('company', 'jobs.company_id', '=', 'company.id');
                $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                    $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                        ->where('resumes_viewed.company_id', '=', $company_id);
                });
                $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                $query->where('jobs.user_id', $user_id);
                $query->where('applications.status', 'active');
                $query->where('applications.hiring_status', 'Yes');
                $totalShortlistedApplications = $query->count();

                $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
                $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
                $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
                $query->join('users', 'applications.user_id', '=', 'users.id');
                $query->join('company', 'jobs.company_id', '=', 'company.id');
                $query->join('countries', 'jobs.job_country', '=', 'countries.id');
                $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                    $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                        ->where('resumes_viewed.company_id', '=', $company_id);
                });
                $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
                $query->where('jobs.user_id', $user_id);
                $query->where('applications.status', 'active');
                $query->where('applications.hiring_status', 'No');
                $totalRejectedApplications = $query->count();
            }

            $currentDate = Carbon::now();
            $lastWeekStartDate = $currentDate->subWeek();
            $lastWeekEndDate = Carbon::now();
            $companyprofileViewCountslastweek = CompanyProfileView::whereBetween('created_at', [$lastWeekStartDate, $lastWeekEndDate])
                ->where('company_id', $company_id)
                ->where('status', 'active')
                ->count();
            $jobslastweek = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                ->whereBetween('jobs.created_at', [$lastWeekStartDate, $lastWeekEndDate])
                ->where('jobs.user_id', $user_id)
                ->where('jobs.company_id', $company_id)
                ->where('jobs.job_status', '=', 'active')
                ->orderByDesc('id')
                ->count();

            $company_followers_lastweek = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')
                ->join('users', 'company_followers.user_id', '=', 'users.id')
                ->join('company', 'company_followers.company_id', '=', 'company.id')
                ->whereBetween('company_followers.created_at', [$lastWeekStartDate, $lastWeekEndDate])
                ->where('company_followers.company_id', $company_id)
                ->where('company_followers.status', 'active')
                ->count();
            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastWeekStartDate, $lastWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $applicants_lastweek = $query->count();

            $last_Week_Start_Date = Carbon::now()->subWeek();
            $lastTwoWeekStartDate = $currentDate->subWeek(1);
            $lastTwoWeekEndDate = $last_Week_Start_Date;
            $companyprofileViewCountslasttwoweek = CompanyProfileView::whereBetween('created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                ->where('company_id', $company_id)
                ->where('status', 'active')
                ->count();
            $jobslasttwoweek = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                ->whereBetween('jobs.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                ->where('jobs.user_id', $user_id)
                ->where('jobs.company_id', $company_id)
                ->where('jobs.job_status', '=', 'active')
                ->orderByDesc('id')
                ->count();
            $company_followers_lasttwoweek = CompanyFollower::select('company_followers.created_at', 'users.name as candidate_name', 'users.profile_image', 'users.role as user_role', 'company.company_name', 'company.id as company_id', 'users.id as candidate_id', 'users.slug as candidate_profile_slug', 'users.current_position as candidate_current_position', 'users.email as candidate_email')
                ->join('users', 'company_followers.user_id', '=', 'users.id')
                ->join('company', 'company_followers.company_id', '=', 'company.id')
                ->whereBetween('company_followers.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                ->where('company_followers.company_id', $company_id)
                ->where('company_followers.status', 'active')
                ->count();
            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $applicants_lasttwoweek = $query->count();

            $Impressions_First_Week = $companyprofileViewCountslasttwoweek;
            $Impressions_Last_Week = $companyprofileViewCountslastweek;
            if ($Impressions_First_Week > 0 && $Impressions_Last_Week > 0) {
                $impression_count = (($Impressions_First_Week - $Impressions_Last_Week) / $Impressions_Last_Week) * 100;
                $companyprofileViewimpressionpercentage = number_format($impression_count, 2) . '%';
            } else {
                $companyprofileViewimpressionpercentage = '0%';
            }

            $Jobs_Impressions_First_Week = $jobslasttwoweek;
            $Jobs_Impressions_Last_Week = $jobslastweek;
            if ($Jobs_Impressions_First_Week > 0 && $Jobs_Impressions_Last_Week > 0) {
                $jobs_impression_count = (($Jobs_Impressions_First_Week - $Jobs_Impressions_Last_Week) / $Jobs_Impressions_Last_Week) * 100;
                $jobsimpressionpercentage = number_format($jobs_impression_count, 2) . '%';
            } else {
                $jobsimpressionpercentage = '0%';
            }

            $company_followers_Impressions_First_Week = $company_followers_lasttwoweek;
            $company_followers_Impressions_Last_Week = $company_followers_lastweek;
            if ($company_followers_Impressions_First_Week > 0 && $company_followers_Impressions_Last_Week > 0) {
                $company_followers_impression_count = (($company_followers_Impressions_First_Week - $company_followers_Impressions_Last_Week) / $company_followers_Impressions_Last_Week) * 100;
                $company_followers_impression_percentage = number_format($company_followers_impression_count, 2) . '%';
            } else {
                $company_followers_impression_percentage = '0%';
            }

            $applicants_Impressions_First_Week = $applicants_lasttwoweek;
            $applicants_Impressions_Last_Week = $applicants_lastweek;
            if ($applicants_Impressions_First_Week > 0 && $applicants_Impressions_Last_Week > 0) {
                $applicants_impression_count = (($applicants_Impressions_First_Week - $applicants_Impressions_Last_Week) / $applicants_Impressions_Last_Week) * 100;
                $applicants_impression_percentage = number_format($applicants_impression_count, 2) . '%';
            } else {
                $applicants_impression_percentage = '0%';
            }

            //$totalcompanyprofileViewimpressionn = $companyprofileViewCountslasttwoweek+$companyprofileViewCountslastweek;

            $company_profileView_Counts_lasttwoweek = $companyprofileViewCountslasttwoweek;
            $company_profileView_Counts_lastweek = $companyprofileViewCountslasttwoweek;
            $company_profileView_Impression_percentage = $companyprofileViewimpressionpercentage;

            $jobs_lasttwoweek = $jobslasttwoweek;
            $jobs_lastweek = $jobslastweek;
            $jobs_Impression_percentage = $jobsimpressionpercentage;

            $company_followers_lasttwoweek = $company_followers_lasttwoweek;
            $company_followers_lastweek = $company_followers_lastweek;
            $company_followers_ImpressionPercentage = $company_followers_impression_percentage;

            $applicantsLasttwoweek = $applicants_lasttwoweek;
            $applicantsLastweek = $applicants_lastweek;
            $applicants_ImpressionPercentage = $applicants_impression_percentage;


            return response()->json([
                'status' => true,
                'company_profile_views_count' => $company_profile_views_count,
                'active_jobs_count' => $active_jobs_count,
                'company_followers_count' => $company_followers_count,
                'company_followers' => $company_followers,
                'totalApplicants' => $totalApplications,
                'totalShortlistedApplicants' => $totalShortlistedApplications,
                'totalRejectedApplicants' => $totalRejectedApplications,
                'company_profileView_Impression_percentage' => $companyprofileViewimpressionpercentage,
                'company_profileView_Counts_lasttwoweek' => $company_profileView_Counts_lasttwoweek,
                'company_profileView_Counts_lastweek' => $company_profileView_Counts_lastweek,
                'jobs_Impression_percentage' => $jobsimpressionpercentage,
                'jobs_lasttwoweek' => $jobs_lasttwoweek,
                'jobs_lastweek' => $jobs_lastweek,
                'company_followers_ImpressionPercentage' => $company_followers_ImpressionPercentage,
                'company_followers_lasttwoweek' => $company_followers_lasttwoweek,
                'company_followers_lastweek' => $company_followers_lastweek,
                'applicants_ImpressionPercentage' => $applicants_ImpressionPercentage,
                'applicants_lasttwoweek' => $applicantsLasttwoweek,
                'applicants_lastweek' => $applicantsLastweek,
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getCompanySecondInsightsChart(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $company_id = $request->company_id;
            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $totalApplications = $query->count();

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $query->where('applications.hiring_status', 'Yes');
            $totalShortlistedApplications = $query->count();

            $totalShortlistedApplicationsPercentage = $totalShortlistedApplications / $totalApplications * 100;

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $query->where('applications.hiring_status', 'No');
            $totalRejectedApplications = $query->count();

            $totalRejectedApplicationsPercentage = $totalRejectedApplications / $totalApplications * 100;

            if ($request->time_type == 'yearly') {
                $applicantsData = Applications::select(DB::raw('YEAR(applications.created_at) as year, SUM(case when applications.hiring_status = "Yes" then 1 else 0 end) as shortlisted_count, SUM(case when applications.hiring_status = "No" then 1 else 0 end) as rejected_count'))
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    ->where('jobs.user_id', $user_id)
                    ->where('applications.status', 'active')
                    ->groupBy(DB::raw('YEAR(applications.created_at)'))
                    ->orderBy(DB::raw('YEAR(applications.created_at)'))
                    ->get();
                $months_labels_arr = array();
                $shortlisted_applicants_counts_arr = array();
                $rejected_applicants_counts_arr = array();
                foreach ($applicantsData as $applicants_Data) {
                    array_push($months_labels_arr, $applicants_Data->year);
                    array_push($shortlisted_applicants_counts_arr, $applicants_Data->shortlisted_count);
                    array_push($rejected_applicants_counts_arr, $applicants_Data->rejected_count);
                }
            } elseif ($request->time_type == 'weekly') {
                $lastWeekStart = Carbon::now()->subWeek()->startOfWeek();
                $lastWeekEnd = Carbon::now()->subWeek()->endOfWeek();
                $applicantsData = Applications::select(DB::raw('WEEK(applications.created_at) as week, SUM(case when applications.hiring_status = "Yes" then 1 else 0 end) as shortlisted_count, SUM(case when applications.hiring_status = "No" then 1 else 0 end) as rejected_count'))
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    ->where('jobs.user_id', $user_id)
                    ->where('applications.status', 'active')
                    ->whereBetween('applications.created_at', [$lastWeekStart, $lastWeekEnd])
                    ->groupBy(DB::raw('WEEK(applications.created_at)'))
                    ->orderBy(DB::raw('WEEK(applications.created_at)'))
                    ->get();

                $months_labels_arr = array();
                $shortlisted_applicants_counts_arr = array();
                $rejected_applicants_counts_arr = array();
                foreach ($applicantsData as $applicants_Data) {
                    array_push($months_labels_arr, $applicants_Data->week);
                    array_push($shortlisted_applicants_counts_arr, $applicants_Data->shortlisted_count);
                    array_push($rejected_applicants_counts_arr, $applicants_Data->rejected_count);
                }
            } else {
                $applicantsData = Applications::select(DB::raw('MONTH(applications.created_at) as month, SUM(case when applications.hiring_status = "Yes" then 1 else 0 end) as shortlisted_count, SUM(case when applications.hiring_status = "No" then 1 else 0 end) as rejected_count'))
                    ->join('jobs', 'applications.job_id', '=', 'jobs.id')
                    ->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id')
                    ->join('users', 'applications.user_id', '=', 'users.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                        $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                            ->where('resumes_viewed.company_id', '=', $company_id);
                    })
                    ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                    ->where('jobs.user_id', $user_id)
                    ->where('applications.status', 'active')
                    ->groupBy(DB::raw('MONTH(applications.created_at)'))
                    ->orderBy(DB::raw('MONTH(applications.created_at)'))
                    ->get();

                $monthsLabels = [
                    'January',
                    'February',
                    'March',
                    'April',
                    'May',
                    'June',
                    'July',
                    'August',
                    'September',
                    'October',
                    'November',
                    'December'
                ];
                $months_labels_arr = array();
                $shortlisted_applicants_counts_arr = array();
                $rejected_applicants_counts_arr = array();
                foreach ($applicantsData as $applicants_Data) {
                    array_push($months_labels_arr, $monthsLabels[$applicants_Data->month - 1]);
                    array_push($shortlisted_applicants_counts_arr, $applicants_Data->shortlisted_count);
                    array_push($rejected_applicants_counts_arr, $applicants_Data->rejected_count);
                }
            }

            $currentDate = Carbon::now();
            $lastWeekStartDate = $currentDate->subWeek();
            $lastWeekEndDate = Carbon::now();
            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastWeekStartDate, $lastWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $applicants_lastweek = $query->count();

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastWeekStartDate, $lastWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.hiring_status', 'Yes');
            $query->where('applications.status', 'active');
            $applicants_shortlisted_lastweek = $query->count();

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastWeekStartDate, $lastWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.hiring_status', 'No');
            $query->where('applications.status', 'active');
            $applicants_rejected_lastweek = $query->count();


            $last_Week_Start_Date = Carbon::now()->subWeek();
            $lastTwoWeekStartDate = $currentDate->subWeek(1);
            $lastTwoWeekEndDate = $last_Week_Start_Date;

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.status', 'active');
            $applicants_lasttwoweek = $query->count();

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.hiring_status', 'Yes');
            $query->where('applications.status', 'active');
            $applicants_shortlisted_lasttwoweek = $query->count();

            $query = Applications::select('applications.*', 'company.company_name as company_name', 'company.id as company_id', 'users.name', 'users.role', 'users.slug as employee_profile_slug', 'users.profile_image', 'countries.country_name as country_name');
            $query->join('jobs', 'applications.job_id', '=', 'jobs.id');
            $query->join('users as job_posted_by_user', 'applications.jobpost_by_userId', '=', 'job_posted_by_user.id');
            $query->join('users', 'applications.user_id', '=', 'users.id');
            $query->join('company', 'jobs.company_id', '=', 'company.id');
            $query->join('countries', 'jobs.job_country', '=', 'countries.id');
            $query->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                    ->where('resumes_viewed.company_id', '=', $company_id);
            });
            $query->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');
            $query->whereBetween('applications.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate]);
            $query->where('jobs.user_id', $user_id);
            $query->where('applications.hiring_status', 'No');
            $query->where('applications.status', 'active');
            $applicants_rejected_lasttwoweek = $query->count();

            $applicants_Impressions_First_Week = $applicants_lasttwoweek;
            $applicants_Impressions_Last_Week = $applicants_lastweek;
            if ($applicants_Impressions_First_Week > 0 && $applicants_Impressions_Last_Week > 0) {
                $applicants_impression_count = (($applicants_Impressions_First_Week - $applicants_Impressions_Last_Week) / $applicants_Impressions_Last_Week) * 100;
                $applicants_impression_percentage = $applicants_impression_count . '%';
            } else {
                $applicants_impression_percentage = '0%';
            }

            $applicants_shortlisted_Impressions_First_Week = $applicants_shortlisted_lasttwoweek;
            $applicants_shortlisted_Impressions_Last_Week = $applicants_shortlisted_lastweek;
            if ($applicants_shortlisted_Impressions_First_Week > 0 && $applicants_shortlisted_Impressions_Last_Week > 0) {
                $applicants_shortlisted_impression_count = (($applicants_shortlisted_Impressions_First_Week - $applicants_shortlisted_Impressions_Last_Week) / $applicants_shortlisted_Impressions_Last_Week) * 100;
                $applicants_shortlisted_impression_percentage = $applicants_shortlisted_impression_count . '%';
            } else {
                $applicants_shortlisted_impression_percentage = '0%';
            }

            $applicants_rejected_Impressions_First_Week = $applicants_rejected_lasttwoweek;
            $applicants_rejected_Impressions_Last_Week = $applicants_rejected_lastweek;
            if ($applicants_rejected_Impressions_First_Week > 0 && $applicants_rejected_Impressions_Last_Week > 0) {
                $applicants_rejected_impression_count = (($applicants_rejected_Impressions_First_Week - $applicants_rejected_Impressions_Last_Week) / $applicants_rejected_Impressions_Last_Week) * 100;
                $applicants_rejected_impression_percentage = $applicants_rejected_impression_count . '%';
            } else {
                $applicants_rejected_impression_percentage = '0%';
            }

            $applicantsLasttwoweek = $applicants_lasttwoweek;
            $applicantsLastweek = $applicants_lastweek;
            $applicants_ImpressionPercentage = $applicants_impression_percentage;

            $applicantsShortlistedLasttwoweek = $applicants_shortlisted_lasttwoweek;
            $applicantsShortlistedLastweek = $applicants_shortlisted_lastweek;
            $applicants_shortlisted_ImpressionPercentage = $applicants_shortlisted_impression_percentage;

            $applicantsRejectedLasttwoweek = $applicants_rejected_lasttwoweek;
            $applicantsRejectedLastweek = $applicants_rejected_lastweek;
            $applicants_rejected_ImpressionPercentage = $applicants_rejected_impression_percentage;

            return response()->json([
                'status' => true,
                'totalApplicants' => $totalApplications,
                'totalShortlistedApplicants' => $totalShortlistedApplicationsPercentage . '%',
                'totalRejectedApplicants' => $totalRejectedApplicationsPercentage . '%',
                'months_labels_data' => $months_labels_arr,
                'shortlisted_applicants_counts_data' => $shortlisted_applicants_counts_arr,
                'rejected_applicants_counts_data' => $rejected_applicants_counts_arr,
                'applicants_lasttwoweek' => $applicantsLasttwoweek,
                'applicants_lastweek' => $applicantsLastweek,
                'applicants_ImpressionPercentage' => $applicants_ImpressionPercentage,
                'applicants_shortlisted_lasttwoweek' => $applicantsShortlistedLasttwoweek,
                'applicants_shortlisted_lastweek' => $applicantsShortlistedLastweek,
                'applicants_shortlisted_ImpressionPercentage' => $applicants_shortlisted_ImpressionPercentage,
                'applicants_rejected_lasttwoweek' => $applicantsRejectedLasttwoweek,
                'applicants_rejected_lastweek' => $applicantsRejectedLastweek,
                'applicants_rejected_ImpressionPercentage' => $applicants_rejected_ImpressionPercentage
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function createOrUpdateCompanyClaimRequest(Request $request)
    {
        $existing_claim_id = ClaimCompany::where(['user_id' => $request->user_id, 'status' => 'requested'])
            ->latest()
            ->pluck('id')
            ->first();
        if (!$existing_claim_id) {
            if (!$request->has('company_id')) {
                return response()->json(['success' => false, 'data' => [], 'message' => 'Please select company first!'], 201);
            }
        }
        $claim_request = ClaimCompany::updateOrCreate(['id' => $existing_claim_id], $request->all());
        return response()->json(['success' => true, 'data' => $claim_request, 'message' => 'Your request has been successfully submitted.'], 201);
    }

    public function approveOrRejectClaimRequest(Request $request)
    {
        // Validate the incoming request
        $validated = $request->validate([
            'company_id' => 'required',
            'user_id' => 'required|exists:users,id',
            'action' => 'required|in:approve,reject', // Ensure action is either 'approve' or 'reject'
        ]);

        $companyId = $validated['company_id'];
        $userId = $validated['user_id'];
        $action = $validated['action']; // 'approve' or 'reject'

        // Find the claim record using the company_id and user_id
        $claim = ClaimCompany::where('company_id', $companyId)
            ->where('user_id', $userId)
            ->first();

        // Check if the claim exists
        if (!$claim) {
            return response()->json([
                'error' => 'Claim not found for the given company and user.'
            ], 404);
        }

        // Approve or reject based on the action
        if ($action === 'approve') {
            // Set claim status to approved (or any other logic for approval)
            $claim->status = 'approved'; // Assuming you have a 'status' column
            $claim->save();

            return response()->json([
                'message' => 'Claim request approved successfully.',
                'claim' => $claim
            ]);
        } elseif ($action === 'reject') {
            // Set claim status to rejected (or any other logic for rejection)
            $claim->status = 'rejected'; // Assuming you have a 'status' column
            $claim->save();

            return response()->json([
                'message' => 'Claim request rejected successfully.',
                'claim' => $claim
            ]);
        }

        // If action is neither 'approve' nor 'reject'
        return response()->json([
            'error' => 'Invalid action.'
        ], 400);
    }

    public function getAllActiveCompanies(Request $request)
    {
        try {
            $companies = Company::where('status', 'active')->get();
            return response()->json(['status' => true, 'data' => $companies], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
