<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Src\EmployerManagement\Infrastructure\Resources\CandidateResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\CandidatesFilter;
use App\Models\ResumesViewed;
use App\Models\User;
use App\Classes\ErrorsClass;
use App\Models\Company;
use Illuminate\Support\Facades\Validator;
use App\Helpers\Helper;
use App\Mail\StaffMemberAdded;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;

//use JWTAuth;

class CandidatesFilterController extends Controller
{

    public function getAllCandidateSearch(Request $request)
    {
        try {

            $id = $request->user_id;

            $company_id = $request->company_id;


            $query = User::with('profile')->select('users.*', 'resume_pdf_path', 'sector_name', 'industries.name as industries_name', 'country_name', 'applicant_id', 'resumes_viewed_user.name as viewed_user_name', 'resumes_viewed_user.id as viewed_user_id')
                ->leftJoin('countries', 'users.where_job_search', '=', 'countries.id')
                ->leftJoin('sector', 'users.sector', '=', 'sector.id')
                ->leftJoin('industries', 'users.industry', '=', 'industries.id')
                ->leftJoin('resumes', function ($join) {
                    $join->on('users.id', '=', 'resumes.user_id')
                        ->where('resumes.default_resume', '=', 1)
                        ->where('resumes.status', '=', 'active');
                })
                ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                    $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                        ->where('resumes_viewed.company_id', '=', $company_id);
                })
                ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id');


            if ($request->profile_status == 'seen') {

                $query->WhereNotNull('resumes_viewed.applicant_id');
            }

            if ($request->profile_status == 'unseen') {

                $query->WhereNull('resumes_viewed.applicant_id');
            }


            if ($request->jobstatus) {
                $query->where('users.job_status', $request->jobstatus);
            }

            if ($request->location) {
                $locations_ids = array_map('intval', explode(',', $request->location));
                $query->whereIn('users.where_job_search', $locations_ids);
            }

            if ($request->skill) {
                $skill_arr = explode(',', $request->skill);
                $query->where(function ($query) use ($skill_arr) {

                    foreach ($skill_arr as $skill_id) {
                        $query->orWhere('users.skills', 'LIKE', "%,$skill_id,%")
                            ->orWhere('users.skills', 'LIKE', "$skill_id,%")
                            ->orWhere('users.skills', 'LIKE', "%,$skill_id")
                            ->orWhere('users.skills', 'LIKE', "%$skill_id%");
                    }
                });
            }

            if ($request->sector) {
                $sector_arr = array_map('intval', explode(',', $request->sector));
                $query->whereIn('users.sector', $sector_arr);
            }



            if ($request->job_type) {
                $job_type_arr = explode(',', $request->job_type);
                $query->where(function ($query) use ($job_type_arr) {

                    foreach ($job_type_arr as $job_type) {
                        $query->orWhere('users.job_type', 'LIKE', "%,$job_type,%")
                            ->orWhere('users.job_type', 'LIKE', "$job_type,%")
                            ->orWhere('users.job_type', 'LIKE', "%,$job_type")
                            ->orWhere('users.job_type', 'LIKE', "%$job_type%");
                    }
                });
            }


            if ($request->experience) {
                $query->where('users.years_of_experience', $request->experience);
            }


            if (isset($request->currency)) {
                $currency = $request->currency; // Get the currency from the request
                $minSalary = (int) $request->minsalary; // Convert minsalary to integer

                $query->where(function ($query) use ($currency, $minSalary) {
                    $query->where('users.currency', $currency)
                        ->where('users.desired_salary', '>=', $minSalary);
                });
            }


            $query->where('users.role', '=', 'employee');
            $query->where('users.status', '!=', 'deleted');
            $query->orderByDesc('users.id');
            $candidate = $query->get();
            $candidates = CandidateResource::collection($candidate);

            return response()->json([
                'status' => true,
                'message' => 'All candidates retrieved successfully',
                'data' => $candidates,
            ], 200);


        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }

    }

    public function getAllCandidateFilter(Request $request, $id, $search_keyword)
    {
        try {
            $user_id = $id;
            $jobFilter = CandidatesFilter::firstOrCreate(
                ['user_id' => $user_id],
                ['user_id' => $user_id, 'section_name' => 'All Resumes']
            );
            if($search_keyword == "null"){
                $filtercandidatedata = CandidatesFilter::where('status', 'active')
            ->where('user_id', $id)
            ->get();
            } else {
                $filtercandidatedata = CandidatesFilter::where('status', 'active')
                ->where('user_id', $id)
                ->where('search_by_keyword',$search_keyword)
                ->get();
            }


            return response()->json([
                'status' => true,
                'message' => 'All candidate filter retrieved successfully',
                'data' => $filtercandidatedata,
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function UpdateSaveCandidateFilter(Request $request)
    {
        try {

            if ($request->filter_id) {
                $candidatefilter = CandidatesFilter::find($request->filter_id);
                $candidatefilter->user_id = $request->user_id;
                $candidatefilter->section_name = $request->section_name;
                $candidatefilter->job_status = isset($request->jobstatus) ? $request->jobstatus : null;
                $candidatefilter->profile_status = isset($request->profile_status) ? $request->profile_status : null;
                $candidatefilter->currency = $request->currency;
                $candidatefilter->salary = $request->minsalary;
                $candidatefilter->experience = $request->experience;
                $candidatefilter->country_id = $request->location;
                $candidatefilter->skills = $request->skill;
                $candidatefilter->sector = $request->sector;
                $candidatefilter->job_type = $request->job_type;
                $candidatefilter->search_by_keyword = $request->searchByKeyword;

                $candidatefilter->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Section name updated successfully',
                    'id' => $request->filter_id,
                    'data' => $candidatefilter
                ], 200);

            } else {

                $candidatefilter = new CandidatesFilter();
                $candidatefilter->user_id = $request->user_id;
                $candidatefilter->section_name = $request->section_name;
                $candidatefilter->job_status = isset($request->jobstatus) ? $request->jobstatus : null;
                $candidatefilter->profile_status = isset($request->profile_status) ? $request->profile_status : null;
                $candidatefilter->currency = $request->currency;
                $candidatefilter->salary = $request->minsalary;
                $candidatefilter->experience = $request->experience;
                $candidatefilter->country_id = $request->location;
                $candidatefilter->skills = $request->skill;
                $candidatefilter->sector = $request->sector;
                $candidatefilter->job_type = $request->job_type;
                $candidatefilter->search_by_keyword = $request->searchByKeyword;


                $candidatefilter->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Section name added successfully',
                    'id' => $candidatefilter->id,
                    'data' => CandidatesFilter::find($candidatefilter->id)

                ], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function DeleteCandidateFilter(Request $request)
    {
        $candidatefilter = CandidatesFilter::find($request->id);
        $candidatefilter->status = 'delete';
        $candidatefilter->save();

        return response()->json([
            'status' => true,
            'message' => 'Candidate Filter Section has been deleted successfully!',
        ], 200);
        try {
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function checkAndUpdateResumesViewed(Request $request)
    {

        try {

            $checkApplicantsCount = ResumesViewed::where('applicant_id', $request->applicant_id)->where('company_id', $request->company_id)->count();

            if ($checkApplicantsCount == 0) {

                $getcurrentcompanyresumcount = Company::select('available_resume_count')->where('id', $request->company_id)->first();

                if ($request->user_role == 'employer' && $getcurrentcompanyresumcount->available_resume_count != 0 && $getcurrentcompanyresumcount->available_resume_count != 100000) {

                    $updatecurrentcompanyresumcount = Company::find($request->company_id);
                    $updatecurrentcompanyresumcount->available_resume_count = $getcurrentcompanyresumcount->available_resume_count - 1;
                    $updatecurrentcompanyresumcount->save();
                }

                $getcurrentuserresumcount = User::select('available_resume_count')->where('id', $request->user_id)->first();

                if ($request->user_role == 'staff' && $getcurrentuserresumcount->available_resume_count != 0 && $getcurrentuserresumcount->available_resume_count != 100000) {
                    $updatecurrentuserresumcount = User::find($request->user_id);
                    $updatecurrentuserresumcount->available_resume_count = $getcurrentuserresumcount->available_resume_count - 1;
                    $updatecurrentuserresumcount->save();
                }

                $insertresumedview = new ResumesViewed();
                $insertresumedview->user_id = $request->user_id;
                $insertresumedview->company_id = $request->company_id;
                $insertresumedview->applicant_id = $request->applicant_id;
                $insertresumedview->status = 'active';
                $insertresumedview->save();

                return response()->json([
                    'status' => true,
                    'company_resume_count' => $request->user_role === 'employer' ? ($getcurrentcompanyresumcount->available_resume_count == 100000 ? $getcurrentcompanyresumcount->available_resume_count : ($getcurrentcompanyresumcount->available_resume_count - 1)) : 0,
                    'user_resume_count' => $request->user_role === 'staff' ? ($getcurrentuserresumcount->available_resume_count == 100000 ? $getcurrentuserresumcount->available_resume_count : ($getcurrentuserresumcount->available_resume_count - 1)) : 0,
                    'message' => 'Resumed viewed added successfully!',
                ], 200);

            } else {

                return response()->json([
                    'status' => false,
                    'message' => 'Resumed data already updated',
                ], 200);
            }

        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }

    }

    public function AddUpdateStaffMember(Request $request)
    {
        try {

            // return Helper::generateRandomPassword();

            if ($request->id) {

                $userdata = User::find($request->id);
                $userdata->name = $request->name;
                $userdata->email = $request->email;
                $userdata->company_id = $request->company_id;
                $userdata->role = $request->role;
                $userdata->available_resume_count = $request->available_resume_count;
                $userdata->created_by_id = $request->created_by_id;
                $userdata->status = $request->status;
                $userdata->save();

                $updatecurrentcompanyresumcount = Company::find($request->company_id);
                $updatecurrentcompanyresumcount->available_resume_count = $request->company_cv_count;
                $updatecurrentcompanyresumcount->save();


                return response()->json([
                    'status' => true,
                    'message' => 'Team member has been updated successfully'
                ], 200);

            } else {

                $validator = Validator::make($request->all(), [
                    'name' => 'required|string',
                    'email' => 'required|email|unique:users,email', // Check for unique email
                    'company_id' => 'required|numeric',
                    'role' => 'required|string',
                    'available_resume_count' => 'required|numeric|min:0',
                    'created_by_id' => 'required|numeric',
                ]);

                // Check if validation fails and return error response with messages
                if ($validator->fails()) {
                    return response()->json([
                        'status' => false,
                        'message' => $validator->errors(),
                        'errors' => $validator->errors(),
                    ], 200);
                }

                $password = Helper::generateRandomPassword();

                $userdata = new User();
                $userdata->name = $request->name;
                $userdata->email = $request->email;
                $userdata->company_id = $request->company_id;
                $userdata->password = Hash::make($password);
                $userdata->view_password = $password;
                $userdata->role = $request->role;
                $userdata->available_resume_count = $request->available_resume_count;
                $userdata->created_by_id = $request->created_by_id;
                $userdata->status = $request->status;
                $userdata->save();

                $updatecurrentcompanyresumcount = Company::find($request->company_id);
                $updatecurrentcompanyresumcount->available_resume_count = $request->company_cv_count;
                $updatecurrentcompanyresumcount->save();

                Mail::to($request->email)->send(
                    new StaffMemberAdded(
                        $request->name,
                        $password,
                        $request->email,
                        $request->available_resume_count
                    )
                );

                return response()->json([
                    'status' => true,
                    'message' => 'Team member has been added successfully'

                ], 200);

            }


        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function getAllStaffMembers(Request $request, $id)
    {

        try {

            $userdata = User::select('id', 'name', 'email', 'available_resume_count', 'status')
                ->where('created_by_id', $id)
                ->where('status', '!=', 'deleted')
                ->orderBy('id', 'desc')
                ->get();


            return response()->json([
                'status' => true,
                'message' => 'Staff data fetch successfully!',
                'data' => $userdata
            ], 200);

        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function DeleteStaff(Request $request)
    {

        $candidatefilter = User::find($request->id);
        $candidatefilter->status = 'deleted';
        $candidatefilter->save();

        $updatecurrentcompanyresumcount = Company::find($request->company_id);
        $updatecurrentcompanyresumcount->available_resume_count = $request->company_cv_count;
        $updatecurrentcompanyresumcount->save();

        return response()->json([
            'status' => true,
            'message' => 'Candidate Filter section has been deleted successfully!',
        ], 200);
        try {
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllCandidateHeaderSearch(Request $request)
    {
        try {

            $value = $request->value;
            $user_id = $request->user_id;
            $company_id = $request->company_id;

            $employee = User::select('users.name', 'users.slug', 'resumes_viewed.applicant_id', 'users.id as user_id', 'resumes_viewed_user.name as viewed_user_name', 'resumes_viewed_user.id as viewed_user_id')
                ->leftJoin('resumes_viewed', function ($join) use ($company_id) {
                    $join->on('users.id', '=', 'resumes_viewed.applicant_id')
                        ->where('resumes_viewed.company_id', '=', $company_id);
                })
                ->leftJoin('users as resumes_viewed_user', 'resumes_viewed.user_id', '=', 'resumes_viewed_user.id')
                ->where('users.name', 'like', '%' . $value . '%')
                ->where('users.role', 'employee')
                ->where('users.status', 'active')
                ->get();


            return response()->json(['status' => true, 'employee' => $employee]);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function AddCandidateFilterSubSection(Request $request, $id)
    {
        try {

            $user_id = $id;
            $jobFilter = CandidatesFilter::firstOrCreate(
                ['user_id' => $user_id],
                ['user_id' => $user_id, 'section_name' => 'All Resumes']
            );


            $filtercandidatedata = CandidatesFilter::where('status', 'active')->where('user_id', $id)->get();

            return response()->json([
                'status' => true,
                'message' => 'All candidate filter retrieved successfully',
                'data' => $filtercandidatedata,
            ], 200);


        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

}
